"""
Endpoints del dashboard
"""

from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Obtener estadísticas del dashboard
    """
    # TODO: Implementar estadísticas reales
    return {
        "total_viajes": 0,
        "total_hoteles": 0,
        "total_transportes": 0,
        "validaciones_pendientes": 0,
        "exportaciones_recientes": 0,
    }


@router.get("/recent-activity")
async def get_recent_activity(
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Obtener actividad reciente
    """
    # TODO: Implementar actividad real
    return {
        "activities": [
            {
                "id": "1",
                "type": "viaje_generado",
                "message": "Se generaron 150 viajes para L1",
                "timestamp": "2025-01-07T10:00:00Z",
            },
            {
                "id": "2", 
                "type": "pliego_procesado",
                "message": "Se procesó el pliego PPT.pdf",
                "timestamp": "2025-01-07T09:30:00Z",
            },
        ]
    }

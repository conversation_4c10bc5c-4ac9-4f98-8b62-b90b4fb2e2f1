"""
Excepciones personalizadas del sistema IMSERSO
"""

from typing import Any, Dict, Optional


class IMSERSOException(Exception):
    """Excepción base del sistema IMSERSO"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(IMSERSOException):
    """Error de validación"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=422,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class NotFoundError(IMSERSOException):
    """Recurso no encontrado"""
    
    def __init__(self, resource: str, identifier: str):
        super().__init__(
            message=f"{resource} con identificador '{identifier}' no encontrado",
            status_code=404,
            error_code="NOT_FOUND",
            details={"resource": resource, "identifier": identifier},
        )


class AuthenticationError(IMSERSOException):
    """Error de autenticación"""
    
    def __init__(self, message: str = "Credenciales inválidas"):
        super().__init__(
            message=message,
            status_code=401,
            error_code="AUTHENTICATION_ERROR",
        )


class AuthorizationError(IMSERSOException):
    """Error de autorización"""
    
    def __init__(self, message: str = "No tiene permisos para realizar esta acción"):
        super().__init__(
            message=message,
            status_code=403,
            error_code="AUTHORIZATION_ERROR",
        )


class FileProcessingError(IMSERSOException):
    """Error procesando archivos"""
    
    def __init__(self, message: str, filename: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Error procesando archivo '{filename}': {message}",
            status_code=422,
            error_code="FILE_PROCESSING_ERROR",
            details={"filename": filename, **(details or {})},
        )


class AIServiceError(IMSERSOException):
    """Error en servicios de IA"""
    
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Error en servicio de IA {service}: {message}",
            status_code=503,
            error_code="AI_SERVICE_ERROR",
            details={"service": service, **(details or {})},
        )


class TripGenerationError(IMSERSOException):
    """Error en generación de viajes"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Error generando viajes: {message}",
            status_code=422,
            error_code="TRIP_GENERATION_ERROR",
            details=details,
        )


class ExportError(IMSERSOException):
    """Error en exportación"""
    
    def __init__(self, format_type: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Error exportando a {format_type}: {message}",
            status_code=422,
            error_code="EXPORT_ERROR",
            details={"format": format_type, **(details or {})},
        )


class DatabaseError(IMSERSOException):
    """Error de base de datos"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Error de base de datos: {message}",
            status_code=500,
            error_code="DATABASE_ERROR",
            details=details,
        )


class ConfigurationError(IMSERSOException):
    """Error de configuración"""
    
    def __init__(self, setting: str, message: str):
        super().__init__(
            message=f"Error de configuración en '{setting}': {message}",
            status_code=500,
            error_code="CONFIGURATION_ERROR",
            details={"setting": setting},
        )


class RateLimitError(IMSERSOException):
    """Error de límite de velocidad"""
    
    def __init__(self, resource: str, limit: int, window: str):
        super().__init__(
            message=f"Límite de velocidad excedido para {resource}: {limit} por {window}",
            status_code=429,
            error_code="RATE_LIMIT_ERROR",
            details={"resource": resource, "limit": limit, "window": window},
        )


class BusinessRuleError(IMSERSOException):
    """Error de regla de negocio"""
    
    def __init__(self, rule: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Violación de regla de negocio '{rule}': {message}",
            status_code=422,
            error_code="BUSINESS_RULE_ERROR",
            details={"rule": rule, **(details or {})},
        )


# Mapeo de códigos de error HTTP a excepciones
HTTP_EXCEPTION_MAP = {
    400: ValidationError,
    401: AuthenticationError,
    403: AuthorizationError,
    404: NotFoundError,
    422: ValidationError,
    429: RateLimitError,
    500: IMSERSOException,
    503: AIServiceError,
}

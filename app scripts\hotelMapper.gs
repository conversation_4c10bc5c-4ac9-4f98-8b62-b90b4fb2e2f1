/**
 * 
 */


function getSheetMappedByHeaders(sheetName){
  var sheetToMap = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName);
  var rangeData = sheetToMap.getDataRange();
  var lastColumn = rangeData.getLastColumn();
  var lastRow = rangeData.getLastRow();
  var values = sheetToMap.getRange(1, 1, lastRow, lastColumn).getValues();
  var _headerValues = values.shift();
  var headers = new Object();
  for (i = 0; i < _headerValues.length; i++){
    headers[_headerValues[i]] = i;
  }
  return [headers, values];
}

function getHotelMap(sheetName){
  var _l = getSheetMappedByHeaders(sheetName);
  var headers = _l[0];
  var values = _l[1];
  var hotelMapper = {};
  values.forEach(row =>{
    hotelMapper[row[headers["CodHotel"]]] = {
      "totalRooms": row[headers["NumPlazasOfert"]],
      "NumPlazasOfert": row[headers["NumPlazasOfert"]],
      "totalBooks": row[headers["NumEstancOfert"]]
    }
  });
  return hotelMapper;
}



function test_hotelMapper(){
  data = getHotelMap("L1-Anexo8");
  Logger.log(data);
}
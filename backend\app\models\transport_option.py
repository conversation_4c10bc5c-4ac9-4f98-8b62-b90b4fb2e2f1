"""
Modelo para opciones de transporte optimizadas
"""

from dataclasses import dataclass
from datetime import time
from typing import Optional


@dataclass
class TransportOption:
    """Opción de transporte optimizada"""
    tipo: str  # A, V, T, E, TR (traslado)
    origen: str
    destino: str
    hora_salida: time
    hora_llegada: time
    precio: float
    distancia_km: int
    codigo_identificador: Optional[str] = None  # Para vuelos/trenes específicos
    es_traslado: bool = False  # Indica si es un traslado aeropuerto/estación ↔ hotel
    medio_traslado: Optional[str] = None  # 'autocar', 'taxi', 'metro'

"""
Optimizador de rutas para viajes IMSERSO
Encuentra las mejores combinaciones de transporte minimizando distancia y coste
"""

import asyncio
import math
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import time
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from loguru import logger

from app.core.database import AsyncSessionLocal
from app.models.master_data import Pro<PERSON>cia, Aeropuerto
from app.models.transport import <PERSON>uel<PERSON>, Tren, RutaAutocar
from app.services.trip_generator import TransportOption


@dataclass
class RouteNode:
    """Nodo en el grafo de rutas"""
    provincia: str
    aeropuerto: Optional[str] = None
    estacion: Optional[str] = None


@dataclass
class RouteEdge:
    """Arista en el grafo de rutas"""
    origen: RouteNode
    destino: RouteNode
    transport_option: TransportOption
    peso: float  # Combinación de distancia, tiempo y precio


class RouteOptimizer:
    """Optimizador de rutas usando algoritmos de grafos"""
    
    def __init__(self):
        # Coordenadas aproximadas de capitales de provincia (para cálculos de distancia)
        self.provincia_coords = {
            '01': (42.8467, -2.6716),   # <PERSON>lava - Vitoria
            '02': (38.9942, -1.8564),   # Albacete
            '03': (38.3452, -0.4810),   # Alicante
            '04': (36.8381, -2.4597),   # Almería
            '05': (40.6566, -4.7016),   # Ávila
            '06': (38.8794, -6.9707),   # Badajoz
            '07': (39.5696, 2.6502),    # Baleares - Palma
            '08': (41.3851, 2.1734),    # Barcelona
            '09': (42.3440, -3.6969),   # Burgos
            '10': (39.4753, -6.3724),   # Cáceres
            '11': (36.5270, -6.2885),   # Cádiz
            '12': (39.9864, -0.0513),   # Castellón
            '13': (38.9848, -3.9254),   # Ciudad Real
            '14': (37.8882, -4.7794),   # Córdoba
            '15': (43.3623, -8.4115),   # A Coruña
            '16': (40.0703, -2.1374),   # Cuenca
            '17': (41.9794, 2.8214),    # Girona
            '18': (37.1773, -3.5986),   # Granada
            '19': (40.6320, -3.1601),   # Guadalajara
            '20': (43.3183, -1.9812),   # Guipúzcoa - San Sebastián
            '21': (37.2614, -6.9447),   # Huelva
            '22': (42.1401, -0.4086),   # Huesca
            '23': (37.7796, -3.7849),   # Jaén
            '24': (42.5987, -5.5671),   # León
            '25': (41.6176, 0.6200),    # Lleida
            '26': (42.2871, -2.5396),   # La Rioja - Logroño
            '27': (43.0097, -7.5567),   # Lugo
            '28': (40.4168, -3.7038),   # Madrid
            '29': (36.7213, -4.4214),   # Málaga
            '30': (37.9922, -1.1307),   # Murcia
            '31': (42.8169, -1.6432),   # Navarra - Pamplona
            '32': (42.3370, -7.8640),   # Ourense
            '33': (43.3614, -5.8593),   # Asturias - Oviedo
            '34': (42.0096, -4.5284),   # Palencia
            '35': (28.1248, -15.4300),  # Las Palmas
            '36': (42.4296, -8.6446),   # Pontevedra
            '37': (40.9701, -5.6635),   # Salamanca
            '38': (28.4636, -16.2518),  # Santa Cruz de Tenerife
            '39': (43.4623, -3.8099),   # Cantabria - Santander
            '40': (40.9429, -4.1088),   # Segovia
            '41': (37.3891, -5.9845),   # Sevilla
            '42': (41.7665, -2.9516),   # Soria
            '43': (41.1189, 1.2445),    # Tarragona
            '44': (40.3456, -1.1063),   # Teruel
            '45': (39.8628, -4.0273),   # Toledo
            '46': (39.4699, -0.3763),   # Valencia
            '47': (41.6523, -4.7245),   # Valladolid
            '48': (43.2627, -2.9253),   # Vizcaya - Bilbao
            '49': (41.5034, -5.7447),   # Zamora
            '50': (41.6488, -0.8891),   # Zaragoza
            '51': (35.8894, -5.3213),   # Ceuta
            '52': (35.2919, -2.9381),   # Melilla
        }
    
    async def find_optimal_route(self, origen: str, destino: str, lote: str, 
                               max_transportes: int = 4) -> List[TransportOption]:
        """
        Encontrar la ruta óptima entre origen y destino
        Prioriza: 1) Menor distancia, 2) Menor precio, 3) Menos transbordos
        """
        
        logger.info(f"🗺️ Optimizando ruta {origen} -> {destino} para {lote}")
        
        try:
            # 1. Verificar si hay ruta directa eficiente
            direct_route = await self._find_direct_route(origen, destino)
            if direct_route and len(direct_route) == 1:
                logger.info(f"✅ Ruta directa encontrada: {direct_route[0].tipo}")
                return direct_route
            
            # 2. Buscar rutas con conexiones optimizadas
            multi_hop_routes = await self._find_multi_hop_routes(origen, destino, max_transportes)
            
            # 3. Evaluar y seleccionar la mejor ruta
            best_route = self._select_best_route(direct_route, multi_hop_routes, lote)
            
            if best_route:
                logger.info(f"✅ Ruta optimizada: {len(best_route)} transportes, {sum(t.distancia_km for t in best_route)}km")
                return best_route
            
            # 4. Fallback: ruta directa en autocar
            logger.warning(f"⚠️ No se encontró ruta optimizada, usando autocar directo")
            return await self._create_fallback_bus_route(origen, destino)
            
        except Exception as e:
            logger.error(f"❌ Error optimizando ruta {origen} -> {destino}: {e}")
            return await self._create_fallback_bus_route(origen, destino)
    
    async def _find_direct_route(self, origen: str, destino: str) -> List[TransportOption]:
        """Buscar rutas directas (un solo transporte)"""
        
        routes = []
        
        async with AsyncSessionLocal() as session:
            # 1. Buscar vuelos directos
            vuelos_query = select(Vuelo).where(
                and_(
                    Vuelo.activo == True,
                    Vuelo.plazas_disponibles > 0
                )
            )
            
            # Mapear provincias a aeropuertos principales
            aeropuerto_origen = await self._get_main_airport_for_province(session, origen)
            aeropuerto_destino = await self._get_main_airport_for_province(session, destino)
            
            if aeropuerto_origen and aeropuerto_destino:
                vuelos_query = vuelos_query.where(
                    and_(
                        Vuelo.aeropuerto_origen == aeropuerto_origen,
                        Vuelo.aeropuerto_destino == aeropuerto_destino
                    )
                )
                
                result = await session.execute(vuelos_query)
                vuelos = result.scalars().all()
                
                for vuelo in vuelos:
                    # Calcular distancia aérea aproximada
                    distancia = self.calculate_air_distance(origen, destino)
                    
                    transport_option = TransportOption(
                        tipo='V',
                        origen=aeropuerto_origen,
                        destino=aeropuerto_destino,
                        hora_salida=vuelo.hora_salida or time(10, 0),
                        hora_llegada=vuelo.hora_llegada or time(12, 0),
                        precio=float(vuelo.precio or 150.0),
                        distancia_km=distancia,
                        codigo_identificador=vuelo.codigo_vuelo
                    )
                    routes.append(transport_option)
            
            # 2. Buscar trenes directos
            trenes_query = select(Tren).where(
                and_(
                    Tren.activo == True,
                    Tren.plazas_disponibles > 0
                )
            )
            
            # Mapear provincias a estaciones principales
            estacion_origen = await self._get_main_station_for_province(origen)
            estacion_destino = await self._get_main_station_for_province(destino)
            
            if estacion_origen and estacion_destino:
                trenes_query = trenes_query.where(
                    and_(
                        Tren.estacion_origen.ilike(f"%{estacion_origen}%"),
                        Tren.estacion_destino.ilike(f"%{estacion_destino}%")
                    )
                )
                
                result = await session.execute(trenes_query)
                trenes = result.scalars().all()
                
                for tren in trenes:
                    # Calcular distancia ferroviaria aproximada
                    distancia = int(self.calculate_ground_distance(origen, destino) * 1.2)  # +20% por rutas ferroviarias
                    
                    transport_option = TransportOption(
                        tipo=tren.tipo or 'T',
                        origen=estacion_origen,
                        destino=estacion_destino,
                        hora_salida=tren.hora_salida or time(9, 0),
                        hora_llegada=tren.hora_llegada or time(14, 0),
                        precio=float(tren.precio or 80.0),
                        distancia_km=distancia,
                        codigo_identificador=tren.numero_tren
                    )
                    routes.append(transport_option)
            
            # 3. Ruta directa en autocar (siempre disponible)
            distancia_carretera = self.calculate_ground_distance(origen, destino)
            
            # Solo incluir autocar directo si es <= 499km (regla IMSERSO)
            if distancia_carretera <= 499:
                tiempo_viaje = max(2, distancia_carretera // 80)  # ~80km/h promedio, mínimo 2h
                
                transport_option = TransportOption(
                    tipo='A',
                    origen=origen,
                    destino=destino,
                    hora_salida=time(8, 0),
                    hora_llegada=time(min(22, 8 + tiempo_viaje), 0),
                    precio=distancia_carretera * 0.12,  # €0.12 por km
                    distancia_km=int(distancia_carretera)
                )
                routes.append(transport_option)
        
        return routes
    
    async def _find_multi_hop_routes(self, origen: str, destino: str, max_transportes: int) -> List[List[TransportOption]]:
        """Buscar rutas con conexiones (múltiples transportes)"""
        
        # Implementar algoritmo de búsqueda de rutas con conexiones
        # Por ahora, implementación simplificada con conexiones principales
        
        routes = []
        
        # Conexiones principales (hubs de transporte)
        hubs = ['28', '08', '41', '46', '48']  # Madrid, Barcelona, Sevilla, Valencia, Bilbao
        
        for hub in hubs:
            if hub != origen and hub != destino:
                try:
                    # Ruta: origen -> hub -> destino
                    leg1 = await self._find_direct_route(origen, hub)
                    leg2 = await self._find_direct_route(hub, destino)
                    
                    if leg1 and leg2:
                        # Combinar las mejores opciones de cada tramo
                        best_leg1 = min(leg1, key=lambda x: x.precio + x.distancia_km * 0.01)
                        best_leg2 = min(leg2, key=lambda x: x.precio + x.distancia_km * 0.01)
                        
                        # Verificar que los horarios sean compatibles (mínimo 1h de conexión)
                        if self._are_connections_compatible(best_leg1, best_leg2):
                            combined_route = [best_leg1, best_leg2]
                            routes.append(combined_route)
                
                except Exception as e:
                    logger.warning(f"⚠️ Error buscando ruta via {hub}: {e}")
                    continue
        
        return routes
    
    def _are_connections_compatible(self, leg1: TransportOption, leg2: TransportOption) -> bool:
        """Verificar si dos tramos son compatibles en horarios"""
        
        if not leg1.hora_llegada or not leg2.hora_salida:
            return True  # Asumir compatible si no hay horarios específicos
        
        # Convertir a minutos para comparar
        llegada_minutos = leg1.hora_llegada.hour * 60 + leg1.hora_llegada.minute
        salida_minutos = leg2.hora_salida.hour * 60 + leg2.hora_salida.minute
        
        # Mínimo 60 minutos de conexión
        return salida_minutos >= llegada_minutos + 60
    
    def _select_best_route(self, direct_routes: List[TransportOption], 
                          multi_hop_routes: List[List[TransportOption]], 
                          lote: str) -> List[TransportOption]:
        """Seleccionar la mejor ruta según criterios de optimización"""
        
        all_routes = []
        
        # Añadir rutas directas
        for route in direct_routes:
            all_routes.append([route])
        
        # Añadir rutas con conexiones
        all_routes.extend(multi_hop_routes)
        
        if not all_routes:
            return []
        
        # Función de puntuación (menor es mejor)
        def route_score(route: List[TransportOption]) -> float:
            total_distance = sum(t.distancia_km for t in route)
            total_price = sum(t.precio for t in route)
            num_connections = len(route) - 1
            
            # Penalizar distancia, precio y conexiones
            score = (
                total_distance * 0.5 +      # Peso por distancia
                total_price * 2.0 +         # Peso por precio
                num_connections * 100       # Penalización por conexiones
            )
            
            # Bonificación por usar AVE/Avión en rutas largas
            if total_distance > 300:
                for transport in route:
                    if transport.tipo in ['V', 'E']:
                        score *= 0.8  # 20% de bonificación
            
            return score
        
        # Seleccionar la ruta con menor puntuación
        best_route = min(all_routes, key=route_score)
        return best_route

    async def _create_fallback_bus_route(self, origen: str, destino: str) -> List[TransportOption]:
        """Crear ruta de autocar como fallback"""

        distancia = self.calculate_ground_distance(origen, destino)
        tiempo_viaje = max(2, distancia // 80)  # ~80km/h promedio

        transport_option = TransportOption(
            tipo='A',
            origen=origen,
            destino=destino,
            hora_salida=time(8, 0),
            hora_llegada=time(min(22, 8 + tiempo_viaje), 0),
            precio=distancia * 0.15,  # €0.15 por km
            distancia_km=int(distancia)
        )

        return [transport_option]

    async def _get_main_airport_for_province(self, session: AsyncSession, provincia: str) -> Optional[str]:
        """Obtener aeropuerto principal para una provincia"""

        # Mapeo de provincias a aeropuertos principales
        provincia_to_airport = {
            '28': 'MAD',  # Madrid
            '08': 'BCN',  # Barcelona
            '41': 'SVQ',  # Sevilla
            '29': 'AGP',  # Málaga
            '46': 'VLC',  # Valencia
            '03': 'ALC',  # Alicante
            '48': 'BIO',  # Bilbao
            '07': 'PMI',  # Baleares
            '35': 'LPA',  # Las Palmas
            '38': 'TFS',  # Tenerife
            '15': 'SCQ',  # A Coruña
            '33': 'OVD',  # Asturias
            '39': 'SDR',  # Santander
            '01': 'VIT',  # Vitoria
            '50': 'ZAZ',  # Zaragoza
            '18': 'GRX',  # Granada
            '11': 'XRY',  # Jerez
            '43': 'REU',  # Reus
        }

        airport_code = provincia_to_airport.get(provincia)
        if airport_code:
            # Verificar que el aeropuerto existe en la base de datos
            result = await session.execute(
                select(Aeropuerto).where(Aeropuerto.codigo_iata == airport_code)
            )
            airport = result.scalar_one_or_none()
            return airport_code if airport else None

        return None

    async def _get_main_station_for_province(self, provincia: str) -> Optional[str]:
        """Obtener estación principal para una provincia"""

        # Mapeo de provincias a estaciones principales
        provincia_to_station = {
            '28': 'Madrid',
            '08': 'Barcelona',
            '41': 'Sevilla',
            '46': 'Valencia',
            '50': 'Zaragoza',
            '47': 'Valladolid',
            '37': 'Salamanca',
            '14': 'Córdoba',
            '23': 'Jaén',
            '13': 'Ciudad Real',
            '16': 'Cuenca',
            '19': 'Guadalajara',
            '45': 'Toledo',
            '02': 'Albacete',
            '30': 'Murcia',
            '03': 'Alicante',
            '12': 'Castellón',
            '44': 'Teruel',
            '22': 'Huesca',
            '25': 'Lleida',
            '17': 'Girona',
            '43': 'Tarragona',
        }

        return provincia_to_station.get(provincia)

    def calculate_ground_distance(self, provincia1: str, provincia2: str) -> float:
        """Calcular distancia por carretera entre dos provincias"""

        if provincia1 == provincia2:
            return 0.0

        coords1 = self.provincia_coords.get(provincia1)
        coords2 = self.provincia_coords.get(provincia2)

        if not coords1 or not coords2:
            # Distancia por defecto si no hay coordenadas
            return 400.0

        # Calcular distancia euclidiana y aplicar factor de corrección por carreteras
        lat1, lon1 = coords1
        lat2, lon2 = coords2

        # Fórmula de Haversine
        R = 6371  # Radio de la Tierra en km

        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)

        a = (math.sin(dlat/2) * math.sin(dlat/2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dlon/2) * math.sin(dlon/2))

        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        # Factor de corrección para carreteras (aproximadamente 1.3x la distancia directa)
        return distance * 1.3

    def calculate_air_distance(self, provincia1: str, provincia2: str) -> int:
        """Calcular distancia aérea entre dos provincias"""

        if provincia1 == provincia2:
            return 0

        coords1 = self.provincia_coords.get(provincia1)
        coords2 = self.provincia_coords.get(provincia2)

        if not coords1 or not coords2:
            return 500  # Distancia por defecto

        # Usar fórmula de Haversine sin factor de corrección
        lat1, lon1 = coords1
        lat2, lon2 = coords2

        R = 6371  # Radio de la Tierra en km

        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)

        a = (math.sin(dlat/2) * math.sin(dlat/2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dlon/2) * math.sin(dlon/2))

        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        return int(distance)

    async def calculate_distance(self, origen: str, destino: str) -> int:
        """Calcular distancia entre dos puntos (método público)"""
        return int(self.calculate_ground_distance(origen, destino))

#!/bin/bash

# Script de entrada para el contenedor backend
set -e

echo "🚀 Iniciando Sistema IMSERSO Backend..."

# Esperar a que PostgreSQL esté disponible
echo "⏳ Esperando PostgreSQL..."
while ! nc -z postgres 5432; do
  sleep 1
done
echo "✅ PostgreSQL disponible"

# Ejecutar migraciones de Alembic
echo "📋 Ejecutando migraciones de base de datos..."
alembic upgrade head

# Verificar si la base de datos está vacía e importar datos iniciales
echo "📊 Verificando datos iniciales..."
python -c "
import asyncio
import sys
from app.core.database import AsyncSessionLocal
from app.models.master_data import <PERSON><PERSON>cia
from sqlalchemy import select, func

async def check_data():
    async with AsyncSessionLocal() as session:
        result = await session.execute(select(func.count(Provincia.codigo)))
        count = result.scalar()
        return count > 0

has_data = asyncio.run(check_data())
if not has_data:
    print('📥 Importando datos iniciales...')
    import subprocess
    result = subprocess.run([sys.executable, 'app/scripts/init_database.py'], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print('✅ Datos iniciales importados correctamente')
    else:
        print(f'❌ Error importando datos: {result.stderr}')
        sys.exit(1)
else:
    print('✅ Datos iniciales ya existen')
"

echo "🎯 Iniciando servidor FastAPI..."

# Ejecutar el servidor
if [ "$ENVIRONMENT" = "development" ]; then
    echo "🔧 Modo desarrollo - Hot reload activado"
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
else
    echo "🚀 Modo producción"
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
fi

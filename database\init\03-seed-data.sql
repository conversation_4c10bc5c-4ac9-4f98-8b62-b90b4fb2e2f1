-- Datos iniciales para el sistema IMSERSO

-- Tipos de transporte
INSERT INTO tipos_transporte (codigo, nombre, descripcion) VALUES
('A', 'Autocar', 'Transporte por carretera en autocar'),
('V', 'Avión', 'Transporte aéreo'),
('T', 'Tren', 'Transporte ferroviario convencional'),
('E', 'AVE', 'Tren de alta velocidad');

-- Tipos de servicio en ruta
INSERT INTO tipos_servicio (codigo, nombre, descripcion) VALUES
('D', 'Desayuno', 'Servicio de desayuno en ruta'),
('A', '<PERSON><PERSON><PERSON><PERSON>', 'Ser<PERSON><PERSON> de almuerzo en ruta'),
('C', 'Cena', 'Servicio de cena en ruta'),
('M', '<PERSON>rien<PERSON>', 'Servicio de merienda en ruta'),
('R', 'Refrigerio', 'Servicio de refrigerio en ruta');

-- Reglas de validación básicas
INSERT INTO reglas_validacion (codigo, nombre, descripcion, tipo, parametros, activa) VALUES
('FIRST_TRANSPORT_BUS', 'Primer transporte vuelta debe ser autocar', 'Si TipoTurno=T, entonces Trans1Vuelta debe ser A', 'transporte', '{"campo": "trans_1_vuelta", "valor_requerido": "A", "condicion": "tipo_turno=T"}', true),
('LAST_TRANSPORT_BUS', 'Último transporte ida debe ser autocar', 'El último transporte de ida debe ser autocar', 'transporte', '{"validar_ultimo_transporte": true}', true),
('DEPARTURE_TIME_MIN', 'Hora mínima de salida', 'Hora de inicio no puede ser antes de las 06:00 (L1/L2) o 08:00 (L3)', 'horario', '{"hora_minima_l1_l2": "06:00", "hora_minima_l3": "08:00"}', true),
('DEPARTURE_TIME_MAX', 'Hora máxima de salida', 'Hora de inicio no puede ser después de las 22:00', 'horario', '{"hora_maxima": "22:00"}', true),
('ARRIVAL_TIME_MAX', 'Hora máxima de llegada', 'Hora de fin no puede ser después de las 22:00 si solo hay un transporte', 'horario', '{"hora_maxima": "22:00", "solo_un_transporte": true}', true),
('KM_LIMIT_BUS_ONLY', 'Límite kilómetros solo autocar', 'Si solo hay autocares, máximo 499km', 'transporte', '{"km_maximo": 499, "solo_autocares": true}', true),
('SERVICE_REQUIRED', 'Servicio obligatorio en ruta', 'Se requiere servicio según horarios de salida y llegada', 'servicio', '{"reglas_horario": true}', true),
('HOTEL_CAPACITY', 'Capacidad de hotel', 'El hotel debe tener suficientes plazas disponibles', 'hotel', '{"validar_capacidad": true}', true),
('SAME_SEASON', 'Temporada única', 'Todas las filas deben tener la misma temporada', 'distribucion', '{"campo": "temporada", "valor_unico": true}', true),
('USE_ALL_AIRPORTS', 'Usar todos los aeropuertos', 'Todos los aeropuertos IATA deben aparecer al menos una vez', 'transporte', '{"validar_todos_aeropuertos": true}', true),
('DISTRIBUTION_MATCH', 'Coincidencia de distribuciones', 'Las plazas usadas deben coincidir con las establecidas', 'distribucion', '{"validar_plazas_establecidas": true}', true);

-- Usuario administrador por defecto
INSERT INTO users (username, email, hashed_password, full_name, is_superuser) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Administrador del Sistema', true);
-- Contraseña: admin123 (cambiar en producción)

-- Algunas provincias de ejemplo (se completarán con datos reales)
INSERT INTO provincias (codigo, nombre, comunidad_autonoma) VALUES
('01', 'Álava', 'País Vasco'),
('02', 'Albacete', 'Castilla-La Mancha'),
('03', 'Alicante', 'Comunidad Valenciana'),
('04', 'Almería', 'Andalucía'),
('05', 'Ávila', 'Castilla y León'),
('06', 'Badajoz', 'Extremadura'),
('07', 'Baleares', 'Islas Baleares'),
('08', 'Barcelona', 'Cataluña'),
('09', 'Burgos', 'Castilla y León'),
('10', 'Cáceres', 'Extremadura'),
('11', 'Cádiz', 'Andalucía'),
('12', 'Castellón', 'Comunidad Valenciana'),
('13', 'Ciudad Real', 'Castilla-La Mancha'),
('14', 'Córdoba', 'Andalucía'),
('15', 'A Coruña', 'Galicia'),
('16', 'Cuenca', 'Castilla-La Mancha'),
('17', 'Girona', 'Cataluña'),
('18', 'Granada', 'Andalucía'),
('19', 'Guadalajara', 'Castilla-La Mancha'),
('20', 'Guipúzcoa', 'País Vasco'),
('21', 'Huelva', 'Andalucía'),
('22', 'Huesca', 'Aragón'),
('23', 'Jaén', 'Andalucía'),
('24', 'León', 'Castilla y León'),
('25', 'Lleida', 'Cataluña'),
('26', 'La Rioja', 'La Rioja'),
('27', 'Lugo', 'Galicia'),
('28', 'Madrid', 'Comunidad de Madrid'),
('29', 'Málaga', 'Andalucía'),
('30', 'Murcia', 'Región de Murcia'),
('31', 'Navarra', 'Comunidad Foral de Navarra'),
('32', 'Ourense', 'Galicia'),
('33', 'Asturias', 'Principado de Asturias'),
('34', 'Palencia', 'Castilla y León'),
('35', 'Las Palmas', 'Canarias'),
('36', 'Pontevedra', 'Galicia'),
('37', 'Salamanca', 'Castilla y León'),
('38', 'Santa Cruz de Tenerife', 'Canarias'),
('39', 'Cantabria', 'Cantabria'),
('40', 'Segovia', 'Castilla y León'),
('41', 'Sevilla', 'Andalucía'),
('42', 'Soria', 'Castilla y León'),
('43', 'Tarragona', 'Cataluña'),
('44', 'Teruel', 'Aragón'),
('45', 'Toledo', 'Castilla-La Mancha'),
('46', 'Valencia', 'Comunidad Valenciana'),
('47', 'Valladolid', 'Castilla y León'),
('48', 'Vizcaya', 'País Vasco'),
('49', 'Zamora', 'Castilla y León'),
('50', 'Zaragoza', 'Aragón'),
('51', 'Ceuta', 'Ceuta'),
('52', 'Melilla', 'Melilla');

-- Aeropuertos principales de España
INSERT INTO aeropuertos (codigo_iata, nombre, ciudad, pais) VALUES
('MAD', 'Adolfo Suárez Madrid-Barajas', 'Madrid', 'España'),
('BCN', 'Barcelona-El Prat', 'Barcelona', 'España'),
('PMI', 'Palma de Mallorca', 'Palma', 'España'),
('LPA', 'Las Palmas', 'Las Palmas de Gran Canaria', 'España'),
('AGP', 'Málaga-Costa del Sol', 'Málaga', 'España'),
('SVQ', 'Sevilla', 'Sevilla', 'España'),
('BIO', 'Bilbao', 'Bilbao', 'España'),
('VLC', 'Valencia', 'Valencia', 'España'),
('ALC', 'Alicante-Elche', 'Alicante', 'España'),
('TFS', 'Tenerife Sur', 'Tenerife', 'España'),
('ACE', 'Lanzarote', 'Arrecife', 'España'),
('FUE', 'Fuerteventura', 'Puerto del Rosario', 'España'),
('SCQ', 'Santiago de Compostela', 'Santiago de Compostela', 'España'),
('OVD', 'Asturias', 'Oviedo', 'España'),
('SDR', 'Santander', 'Santander', 'España'),
('VIT', 'Vitoria', 'Vitoria-Gasteiz', 'España'),
('ZAZ', 'Zaragoza', 'Zaragoza', 'España'),
('GRX', 'Granada', 'Granada', 'España'),
('XRY', 'Jerez', 'Jerez de la Frontera', 'España'),
('REU', 'Reus', 'Reus', 'España');

-- Zonas de destino de ejemplo
INSERT INTO zonas_destino (codigo, nombre, descripcion) VALUES
('AND', 'Andalucía', 'Destinos en la comunidad autónoma de Andalucía'),
('CAT', 'Cataluña', 'Destinos en la comunidad autónoma de Cataluña'),
('VAL', 'Valencia', 'Destinos en la Comunidad Valenciana'),
('GAL', 'Galicia', 'Destinos en la comunidad autónoma de Galicia'),
('CAN', 'Canarias', 'Destinos en las Islas Canarias'),
('BAL', 'Baleares', 'Destinos en las Islas Baleares'),
('MAD', 'Madrid', 'Destinos en la Comunidad de Madrid'),
('CYL', 'Castilla y León', 'Destinos en Castilla y León'),
('CLM', 'Castilla-La Mancha', 'Destinos en Castilla-La Mancha'),
('EXT', 'Extremadura', 'Destinos en Extremadura'),
('ARA', 'Aragón', 'Destinos en Aragón'),
('AST', 'Asturias', 'Destinos en el Principado de Asturias'),
('CAN_REG', 'Cantabria', 'Destinos en Cantabria'),
('RIO', 'La Rioja', 'Destinos en La Rioja'),
('MUR', 'Murcia', 'Destinos en la Región de Murcia'),
('NAV', 'Navarra', 'Destinos en la Comunidad Foral de Navarra'),
('PVA', 'País Vasco', 'Destinos en el País Vasco');

-- Nota: Los datos completos del Google Sheets se importarán automáticamente
-- al iniciar el contenedor usando el script de Python init_database.py
-- Este script se ejecuta después de la creación de las tablas.

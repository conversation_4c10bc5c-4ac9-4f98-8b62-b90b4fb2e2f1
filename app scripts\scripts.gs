function getHotelCodeNameMapper(){
  var sheet = getSheetMappedByHeaders("L3-Anexo8--test");
  var headers = sheet[0];
  var values = sheet[1];
  var nameMapper = {};
  values.forEach(row =>{
    nameMapper[row[headers["ZonaDest"]]+"-"+row[headers["DenHotel"]]] = row[headers["CodHotel"]]
  })
  console.log(nameMapper);
  return nameMapper;
}

function fillHotelCodes(){
  var nameMapper = getHotelCodeNameMapper();
  var ui = SpreadsheetApp.getUi();
  var sheetData = rangeData.getValues();
  for (rowIndex = 1; rowIndex <= sheetData.length; rowIndex++){
    var rowValues = sheetData[rowIndex];
    if (!rowValues || !rowValues[headers.TipoTurno]){
      break;
    }
    hotel_code = nameMapper[rowValues[headers.ZonaDest] + '-' + rowValues[headers.CodHotel1Destino]]
    sheet.getRange(rowIndex+1, headers.CodHotel1Destino+1).setValue(hotel_code);
    //sheet.getRange(rowIndex+1, headers.ProvOrig+1).setValue(provincesMap.codeToName(rowValues[headers.CodProvOrig]));
  }
}

function fixDomAgencia(){
  var ui = SpreadsheetApp.getUi();
  var sheetData = rangeData.getValues();
  for (rowIndex = 1; rowIndex <= sheetData.length; rowIndex++){
    var rowValues = sheetData[rowIndex];
    if (!rowValues || !rowValues[headers.CodAgencia]){
      break;
    }
    sheet.getRange(rowIndex+1, headers.DomAgencia+1).setValue(sanitize(rowValues[headers.DomAgencia]));
  }
}

function fillProvNames(){
  var provincesMap = getProvincesMap();
  var ui = SpreadsheetApp.getUi();
  var sheetData = rangeData.getValues();
  for (rowIndex = 1; rowIndex <= sheetData.length; rowIndex++){
    var rowValues = sheetData[rowIndex];
    if (!rowValues || !rowValues[headers.TipoTurno]){
      break;
    }
    sheet.getRange(rowIndex+1, headers.ProvHotel+1).setValue(provincesMap.codeToName(rowValues[headers.CodProvHotel]));
    sheet.getRange(rowIndex+1, headers.ProvOrig+1).setValue(provincesMap.codeToName(rowValues[headers.CodProvOrig]));
  }
}

function getAutMapper(){
  /** 
   * Necesatio para poder poner el código autonómico
   */
  var sheet = getSheetMappedByHeaders("Municipio")
  var headers = sheet[0];
  var values = sheet[1];
  var autMapper = {};
  values.forEach(row =>{
    autMapper[row[headers["CPRO"]]] = row[headers["CODAUTO"]];
  });
  console.log(autMapper);
  return autMapper;
}


function cleanOutcomeSheet(){
  /** 
   * Elimina todos los datos de la hoja
   * */
  var curLastRow = sheet.getRange("A2:A").getLastRow()-1
  sheet.getRange(2, 1, curLastRow, lastColumn).clearContent().setBorder(false, false, false, false, false, false).setFontSize(11).setFontFamily('Calibri').setFontWeight('normal');
  try{
    sheet.getRange(1, 1, curLastRow, lastColumn).applyRowBanding();
  } catch (error){
    Logger.log('No se puede aplicar esto');
  }
  stablishNumReg();
}

function stablishNumReg(){
  var curLastRow = sheet.getRange("A2:A").getLastRow()-1
  var numregs = [];
  for (rowIndex = 1; rowIndex <= curLastRow; rowIndex++){
    numregs.push([rowIndex]);
  }
  sheet.getRange("A2:A").setValues(numregs);
}

function stablishFormulas(){
  /**
   * Establece las fórmulas de autocompletado
   */
  for (rowIndex = 2; rowIndex <= lastRow; rowIndex++){
    sheet.getRange(rowIndex, headers.ProvHotel+1).setFormula("=VLOOKUP(F"+rowIndex+";Provincia!$A$2:$B$81;2;FALSE)");
    sheet.getRange(rowIndex, headers.ProvOrig+1).setFormula("=VLOOKUP(I"+rowIndex+";Provincia!$A$2:$B$81;2;FALSE)");
  }
}

function fillTimes(){
  var times = [];
  for (h=0;h<24;h++){
    hour = h*100;
    for (m=0;m<60;m++){
      times.push([hour+m])
    }
  }
  Logger.log(times);
  sheet.getRange(2, 3, times.length).setValues(times);
}

"""
Servicio para gestionar traslados aeropuerto/estación ↔ hotel
"""

from typing import List, Optional, Tuple
from dataclasses import dataclass
from datetime import time, timedelta
from loguru import logger

from app.services.distance_calculator import DistanceCalculator, DistanceResult


@dataclass
class TransferOption:
    """Opción de traslado"""
    tipo: str  # 'TR' para traslado
    medio: str  # 'autocar', 'taxi', 'metro', 'bus'
    origen: str
    destino: str
    distancia_km: int
    duracion_minutos: int
    precio: float
    hora_salida: Optional[time] = None
    hora_llegada: Optional[time] = None
    es_traslado: bool = True


class TransferService:
    """Servicio para calcular y gestionar traslados"""
    
    def __init__(self):
        self.distance_calculator = DistanceCalculator()
        
        # Precios por kilómetro según medio de transporte
        self.precio_por_km = {
            'autocar': 0.08,  # €0.08/km - más económico para grupos
            'taxi': 1.20,     # €1.20/km - más caro pero directo
            'metro': 0.05,    # €0.05/km - muy económico si disponible
            'bus': 0.06,      # €0.06/km - económico y frecuente
        }
        
        # Velocidades promedio por medio (km/h)
        self.velocidades = {
            'autocar': 40,    # Tráfico urbano
            'taxi': 35,       # Tráfico urbano + paradas
            'metro': 30,      # Incluye esperas y transbordos
            'bus': 25,        # Paradas frecuentes
        }
        
        # Ciudades con metro/transporte público eficiente
        self.ciudades_con_metro = {
            '28': ['metro', 'bus'],      # Madrid
            '08': ['metro', 'bus'],      # Barcelona
            '46': ['metro', 'bus'],      # Valencia
            '48': ['metro', 'bus'],      # Bilbao
            '41': ['metro', 'bus'],      # Sevilla (metro ligero)
            '29': ['bus'],               # Málaga
            '07': ['bus'],               # Palma
            '35': ['bus'],               # Las Palmas
            '38': ['bus'],               # Tenerife
        }
    
    async def calculate_transfers(self, aeropuerto_o_estacion: str, 
                                hotel_coords: Tuple[float, float],
                                provincia_hotel: str) -> List[TransferOption]:
        """
        Calcular opciones de traslado desde aeropuerto/estación al hotel
        """
        
        logger.info(f"🚌 Calculando traslados desde {aeropuerto_o_estacion} al hotel")
        
        try:
            # Calcular distancia real
            distance_result = await self.distance_calculator.calculate_transfer_distance(
                aeropuerto_o_estacion, hotel_coords
            )
            
            if not distance_result.success:
                logger.warning(f"⚠️ No se pudo calcular distancia real, usando estimación")
                distance_result.distance_km = 15  # Distancia por defecto
                distance_result.duration_minutes = 30
            
            # Generar opciones de traslado
            transfer_options = []
            
            # 1. Autocar (siempre disponible, opción preferida para grupos)
            autocar_option = self._create_transfer_option(
                'autocar', aeropuerto_o_estacion, 'hotel', 
                distance_result, provincia_hotel
            )
            transfer_options.append(autocar_option)
            
            # 2. Taxi (siempre disponible, más caro)
            taxi_option = self._create_transfer_option(
                'taxi', aeropuerto_o_estacion, 'hotel',
                distance_result, provincia_hotel
            )
            transfer_options.append(taxi_option)
            
            # 3. Transporte público (si está disponible en la ciudad)
            public_transport_options = self._get_public_transport_options(
                provincia_hotel, aeropuerto_o_estacion, distance_result
            )
            transfer_options.extend(public_transport_options)
            
            # Ordenar por precio (más económico primero)
            transfer_options.sort(key=lambda x: x.precio)
            
            logger.info(f"✅ {len(transfer_options)} opciones de traslado calculadas")
            return transfer_options
            
        except Exception as e:
            logger.error(f"❌ Error calculando traslados: {e}")
            # Devolver opción básica de autocar
            return [self._create_fallback_transfer(aeropuerto_o_estacion)]
    
    def _create_transfer_option(self, medio: str, origen: str, destino: str,
                              distance_result: DistanceResult, 
                              provincia: str) -> TransferOption:
        """Crear opción de traslado específica"""
        
        distancia = distance_result.distance_km
        
        # Calcular precio
        precio_base = distancia * self.precio_por_km[medio]
        
        # Ajustes de precio según contexto
        if medio == 'autocar':
            # Precio mínimo para autocares
            precio = max(precio_base, 8.0)
        elif medio == 'taxi':
            # Precio mínimo + bajada de bandera
            precio = max(precio_base, 15.0) + 3.0
        else:
            precio = max(precio_base, 2.0)
        
        # Calcular duración
        velocidad = self.velocidades[medio]
        duracion = max(15, int((distancia / velocidad) * 60))  # Mínimo 15 minutos
        
        # Ajustar duración según distancia y medio
        if distancia > 30:  # Distancias largas
            if medio in ['metro', 'bus']:
                duracion += 20  # Tiempo extra para transporte público
        
        return TransferOption(
            tipo='TR',
            medio=medio,
            origen=origen,
            destino=destino,
            distancia_km=distancia,
            duracion_minutos=duracion,
            precio=round(precio, 2)
        )
    
    def _get_public_transport_options(self, provincia: str, origen: str,
                                    distance_result: DistanceResult) -> List[TransferOption]:
        """Obtener opciones de transporte público disponibles"""
        
        options = []
        
        # Verificar si la provincia tiene transporte público eficiente
        available_transport = self.ciudades_con_metro.get(provincia, [])
        
        for transport_type in available_transport:
            # Solo incluir si la distancia es razonable para transporte público
            if distance_result.distance_km <= 25:  # Máximo 25km para transporte público
                option = self._create_transfer_option(
                    transport_type, origen, 'hotel', distance_result, provincia
                )
                options.append(option)
        
        return options
    
    def _create_fallback_transfer(self, origen: str) -> TransferOption:
        """Crear traslado de fallback cuando no se puede calcular"""
        
        return TransferOption(
            tipo='TR',
            medio='autocar',
            origen=origen,
            destino='hotel',
            distancia_km=15,
            duracion_minutos=30,
            precio=12.0
        )
    
    def calculate_transfer_times(self, main_transport_arrival: time,
                               transfer_duration: int) -> Tuple[time, time]:
        """
        Calcular horarios de traslado basados en llegada del transporte principal
        
        Args:
            main_transport_arrival: Hora de llegada del vuelo/tren
            transfer_duration: Duración del traslado en minutos
            
        Returns:
            Tuple[hora_salida_traslado, hora_llegada_hotel]
        """
        
        # Tiempo de espera en aeropuerto/estación (recogida equipaje, etc.)
        waiting_time = 30  # 30 minutos
        
        # Hora de salida del traslado
        departure_time = self._add_minutes_to_time(main_transport_arrival, waiting_time)
        
        # Hora de llegada al hotel
        arrival_time = self._add_minutes_to_time(departure_time, transfer_duration)
        
        return departure_time, arrival_time
    
    def _add_minutes_to_time(self, base_time: time, minutes: int) -> time:
        """Añadir minutos a una hora"""
        
        from datetime import datetime, timedelta
        
        # Convertir time a datetime para poder sumar
        base_datetime = datetime.combine(datetime.today(), base_time)
        new_datetime = base_datetime + timedelta(minutes=minutes)
        
        return new_datetime.time()
    
    def get_best_transfer_option(self, transfer_options: List[TransferOption],
                               criteria: str = 'economico') -> TransferOption:
        """
        Seleccionar la mejor opción de traslado según criterio
        
        Args:
            transfer_options: Lista de opciones disponibles
            criteria: 'economico', 'rapido', 'comodo'
        """
        
        if not transfer_options:
            return self._create_fallback_transfer('unknown')
        
        if criteria == 'economico':
            # Más barato
            return min(transfer_options, key=lambda x: x.precio)
        
        elif criteria == 'rapido':
            # Más rápido
            return min(transfer_options, key=lambda x: x.duracion_minutos)
        
        elif criteria == 'comodo':
            # Priorizar autocar > taxi > transporte público
            priority = {'autocar': 1, 'taxi': 2, 'metro': 3, 'bus': 4}
            return min(transfer_options, key=lambda x: priority.get(x.medio, 5))
        
        else:
            # Por defecto, económico
            return min(transfer_options, key=lambda x: x.precio)
    
    def estimate_total_transfer_cost(self, num_passengers: int, 
                                   transfer_option: TransferOption) -> float:
        """Estimar coste total del traslado para un número de pasajeros"""
        
        if transfer_option.medio == 'autocar':
            # Autocar: precio fijo independiente del número de pasajeros
            return transfer_option.precio
        
        elif transfer_option.medio == 'taxi':
            # Taxi: calcular número de taxis necesarios (4 pasajeros por taxi)
            num_taxis = (num_passengers + 3) // 4
            return transfer_option.precio * num_taxis
        
        else:
            # Transporte público: precio por persona
            return transfer_option.precio * num_passengers

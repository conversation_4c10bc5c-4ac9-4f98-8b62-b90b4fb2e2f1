function readImportFile(fileName) {
  var folders = DriveApp.getFoldersByName("APPImporter")
  while (folders.hasNext()){
    var folder = folders.next()
    files = folder.getFilesByName(fileName)
    while (files.hasNext()){
      var file = files.next()
      docContent = file.getBlob().getDataAsString();
      return docContent;
    }
  }
}

function txtToMatrix(fieldsSize, fileName){
  result = [];
  content = readImportFile(fileName).split('\n');
  for (var i=0; i < content.length; i++){
    row = [];
    offset = 0;
    for (var fieldIndex=0; fieldIndex<fieldsSize.length;fieldIndex++){
      fieldSpecs = fieldsSize[fieldIndex];
      fieldSize = fieldSpecs[1];
      row.push(content[i].slice(offset, offset+fieldSize).trim());
      offset = offset + fieldSize;
    }
    result.push(row);
  }
  return result;
}

function imp8Anexo(){
  data = txtToMatrix(anexo8FieldSpec, "MSanexo8lote3.txt");
  sheet.getRange(2, 1, data.length, data[0].length).setValues(data);
}

function imp7Anexo(){
  data = txtToMatrix(anexo7FieldSpec, "MSanexo7lote3.txt");
  for (var i=0;i<data.length;i++){
    r = data[i];
    r.splice(4, 0, null);
    r.splice(7, 0, null);
  }
  sheet.getRange(2, 1, data.length, data[0].length).setValues(data);
}
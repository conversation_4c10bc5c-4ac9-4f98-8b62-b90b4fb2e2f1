{
	"info": {
		"_postman_id": "12345678-1234-1234-1234-123456789abc",
		"name": "Sistema IMSERSO API",
		"description": "Colección completa para probar todas las funcionalidades del Sistema IMSERSO\n\n## Funcionalidades principales:\n- 🏥 Health Check\n- 🔐 Autenticación\n- 📊 Dashboard y métricas\n- 📋 Procesamiento de pliegos\n- 🏨 Gestión de hoteles\n- 🚌 Gestión de transportes\n- ✈️ Generación automática de viajes\n- ✅ Validaciones con 47+ reglas\n- 📤 Exportación de resultados",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"item": [
		{
			"name": "🏥 Health & System",
			"item": [
				{
					"name": "Health Check",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/v1/health",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "health"]
						},
						"description": "Verifica el estado del sistema y la conexión a la base de datos"
					}
				},
				{
					"name": "API Root Info",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/",
							"host": ["{{base_url}}"]
						},
						"description": "Información básica de la API"
					}
				}
			]
		},
		{
			"name": "🔐 Autenticación",
			"item": [
				{
					"name": "Login",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    pm.environment.set('access_token', response.access_token);",
									"    pm.environment.set('token_type', response.token_type);",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/x-www-form-urlencoded"
							}
						],
						"body": {
							"mode": "urlencoded",
							"urlencoded": [
								{
									"key": "username",
									"value": "<EMAIL>",
									"type": "text"
								},
								{
									"key": "password",
									"value": "admin123",
									"type": "text"
								}
							]
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/login",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "login"]
						}
					}
				},
				{
					"name": "Crear Usuario",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"username\": \"test_user\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"Usuario de Prueba\",\n    \"password\": \"test123\"\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/register",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "register"]
						}
					}
				}
			]
		},
		{
			"name": "📊 Dashboard",
			"item": [
				{
					"name": "Estadísticas Generales",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "{{token_type}} {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/dashboard/stats",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "dashboard", "stats"]
						}
					}
				},
				{
					"name": "Métricas de Viajes",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "{{token_type}} {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/dashboard/viajes-metrics",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "dashboard", "viajes-metrics"]
						}
					}
				}
			]
		}

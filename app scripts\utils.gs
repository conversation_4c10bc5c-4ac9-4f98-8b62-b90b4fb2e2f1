// Compiled using ts2gas 3.6.4 (TypeScript 4.1.5)
/*

    Just a bunch of utils for doing code more pragmatic
    Honestly, this should be already implemented on new Javascript versions.
 */
/**
 * An alias for the dirty solution of != undefined.
 *
 * @param variable Variable to check if is defined
 * @returns true if is defined, false if it's not.
 */
function isDefined(variable) {
    if (variable == undefined) {
        return false;
    }
    return true;
}
/**
 * Just an alias for the dirty solution of == undefined.
 *
 * @param variable Variable to check is it's not defined
 * @return true if is not defined, false if it is.
 */
function isNotDefined(variable) {
    return !isDefined(variable);
}
/**
 * Return the related sheets for this type of pack
 * A pack is just a bundle of options, it could be L1, L2 or L3.
 */
function getRelatedSheets(sheetName) {
    var sheetProperties = sheetName.split("-", 3);
    var prefix = sheetProperties[0];
    var sufix = "";
    if (sheetProperties.length == 3){
      sufix = "-" + sheetProperties[2];
    }
    return {
        hotelSheetName: prefix + "-" + "Anexo8" + sufix,
        withTransport: prefix + "-" + settings.pack.withTransport,
        withoutTransport: prefix + "-" + settings.pack.withoutTransport,
        sheetType: prefix,
    };
}
/**
 *
 * @param sheetName Name for this sheet, for example "L1-OUTCOME-PRO"
 */
function isValidOutcomeSheet(sheetName) {
    var sheetNameArray = sheetName.split("-");
    if (sheetNameArray.indexOf("OUTCOME") > -1) {
        return true
    }
    if (sheetNameArray.indexOf("Anexo7") > -1) {
        return true;
    }
    return false;
}
/**
 * This is just a function to test this through the UI
 */
function testIsDefined() {
    ["", 0, {}, []].forEach(function (value) {
        assertTrue(isDefined(value));
    });
}

# ESTRUCTURA DEL PROYECTO IMSERSO

```
imserso-system/
├── docker-compose.yml
├── docker-compose.dev.yml
├── .env.example
├── .env
├── README.md
├── docs/
│   ├── api/
│   ├── database/
│   └── deployment/
├── backend/
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── pyproject.toml
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── core/
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── database.py
│   │   │   ├── security.py
│   │   │   └── exceptions.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── base.py
│   │   │   ├── user.py
│   │   │   ├── pliego.py
│   │   │   ├── hotel.py
│   │   │   ├── transport.py
│   │   │   ├── viaje.py
│   │   │   └── validation.py
│   │   ├── schemas/
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── pliego.py
│   │   │   ├── hotel.py
│   │   │   ├── transport.py
│   │   │   ├── viaje.py
│   │   │   └── validation.py
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── deps.py
│   │   │   └── v1/
│   │   │       ├── __init__.py
│   │   │       ├── auth.py
│   │   │       ├── pliegos.py
│   │   │       ├── hoteles.py
│   │   │       ├── transportes.py
│   │   │       ├── viajes.py
│   │   │       ├── validaciones.py
│   │   │       ├── exportacion.py
│   │   │       └── dashboard.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── ai_service.py
│   │   │   ├── pdf_processor.py
│   │   │   ├── validation_engine.py
│   │   │   ├── trip_generator.py
│   │   │   ├── route_optimizer.py
│   │   │   ├── export_service.py
│   │   │   └── import_service.py
│   │   ├── utils/
│   │   │   ├── __init__.py
│   │   │   ├── validators.py
│   │   │   ├── formatters.py
│   │   │   ├── file_handlers.py
│   │   │   └── constants.py
│   │   └── tests/
│   │       ├── __init__.py
│   │       ├── conftest.py
│   │       ├── test_api/
│   │       ├── test_services/
│   │       └── test_utils/
│   └── alembic/
│       ├── versions/
│       ├── env.py
│       └── alembic.ini
├── frontend/
│   ├── Dockerfile
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   ├── index.html
│   ├── public/
│   ├── src/
│   │   ├── main.tsx
│   │   ├── App.tsx
│   │   ├── components/
│   │   │   ├── common/
│   │   │   ├── layout/
│   │   │   ├── forms/
│   │   │   └── tables/
│   │   ├── pages/
│   │   │   ├── Dashboard/
│   │   │   ├── Pliegos/
│   │   │   ├── Hoteles/
│   │   │   ├── Transportes/
│   │   │   ├── Viajes/
│   │   │   └── Exportacion/
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   ├── auth.ts
│   │   │   └── types.ts
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── styles/
│   └── dist/
├── database/
│   ├── init/
│   │   ├── 01-init.sql
│   │   ├── 02-extensions.sql
│   │   └── 03-seed-data.sql
│   └── backups/
├── nginx/
│   ├── Dockerfile
│   ├── nginx.conf
│   └── ssl/
├── redis/
│   └── redis.conf
└── storage/
    ├── uploads/
    ├── exports/
    └── temp/
```

## DESCRIPCIÓN DE COMPONENTES

### Backend (FastAPI)
- **core/**: Configuración central, base de datos, seguridad
- **models/**: Modelos SQLAlchemy para PostgreSQL
- **schemas/**: Esquemas Pydantic para validación de APIs
- **api/**: Endpoints REST organizados por versión
- **services/**: Lógica de negocio y servicios externos
- **utils/**: Utilidades y helpers

### Frontend (React + TypeScript)
- **components/**: Componentes reutilizables
- **pages/**: Páginas principales de la aplicación
- **services/**: Comunicación con APIs
- **hooks/**: Custom hooks de React

### Infraestructura
- **database/**: Scripts de inicialización y migraciones
- **nginx/**: Configuración del proxy reverso
- **storage/**: Almacenamiento de archivos

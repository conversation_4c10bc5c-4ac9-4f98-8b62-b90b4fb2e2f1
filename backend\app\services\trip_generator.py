"""
Motor de generación inteligente de viajes IMSERSO
Optimiza rutas, precios y cumple todas las condiciones del IMSERSO
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import time, datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from loguru import logger

from app.core.database import AsyncSessionLocal
from app.core.exceptions import TripGenerationError, BusinessRuleError
from app.models.master_data import Provincia, DistribucionPlaza, Aeropuerto
from app.models.hotel import Hotel, HotelDisponibilidad
from app.models.transport import Vuelo, Tren, RutaAutocar
from app.models.viaje import Viaje
from app.services.route_optimizer import RouteOptimizer
from app.services.validation_engine import ValidationEngine


@dataclass
class TripRequirement:
    """Requisito de viaje a generar"""
    lote: str  # L1, L2, L3
    provincia_origen: str
    zona_destino: str
    dias_turno: int
    tipo_turno: str  # T, S
    plazas_requeridas: int
    temporada: str


@dataclass
class TransportOption:
    """Opción de transporte optimizada"""
    tipo: str  # A, V, T, E
    origen: str
    destino: str
    hora_salida: time
    hora_llegada: time
    precio: float
    distancia_km: int
    codigo_identificador: Optional[str] = None  # Para vuelos/trenes específicos


@dataclass
class OptimizedTrip:
    """Viaje optimizado generado"""
    requirement: TripRequirement
    hotel: Hotel
    transportes_ida: List[TransportOption]
    transportes_vuelta: List[TransportOption]
    precio_total: float
    km_total_ida: int
    km_total_vuelta: int
    servicios_ruta: Dict[str, str]
    num_reg: int


class TripGenerator:
    """Generador inteligente de viajes IMSERSO"""
    
    def __init__(self):
        self.route_optimizer = RouteOptimizer()
        self.validation_engine = ValidationEngine()
        
        # Configuración de optimización
        self.max_transportes_por_direccion = 4
        self.max_km_solo_autocar = 499
        self.hora_minima_salida = {"L1": time(6, 0), "L2": time(6, 0), "L3": time(8, 0)}
        self.hora_maxima_llegada = time(22, 0)
    
    async def generate_trips_for_lote(self, lote: str, temporada: str = "2025") -> List[OptimizedTrip]:
        """
        Generar todos los viajes para un lote específico
        """
        logger.info(f"🚀 Iniciando generación de viajes para {lote} - {temporada}")
        
        try:
            # 1. Obtener requisitos de distribución de plazas
            requirements = await self._get_trip_requirements(lote, temporada)
            logger.info(f"📋 Encontrados {len(requirements)} requisitos de viaje")
            
            # 2. Generar viajes optimizados
            optimized_trips = []
            for i, requirement in enumerate(requirements):
                logger.info(f"🔄 Procesando requisito {i+1}/{len(requirements)}: {requirement.provincia_origen} -> {requirement.zona_destino}")
                
                try:
                    trip = await self._generate_optimized_trip(requirement, i + 1)
                    if trip:
                        optimized_trips.append(trip)
                        logger.info(f"✅ Viaje generado: {trip.num_reg}")
                    else:
                        logger.warning(f"⚠️ No se pudo generar viaje para {requirement.provincia_origen} -> {requirement.zona_destino}")
                
                except Exception as e:
                    logger.error(f"❌ Error generando viaje {i+1}: {e}")
                    continue
            
            logger.info(f"✅ Generación completada: {len(optimized_trips)} viajes creados")
            return optimized_trips
            
        except Exception as e:
            logger.error(f"❌ Error en generación de viajes para {lote}: {e}")
            raise TripGenerationError(f"Error generando viajes para {lote}: {str(e)}")
    
    async def _get_trip_requirements(self, lote: str, temporada: str) -> List[TripRequirement]:
        """Obtener requisitos de viajes desde distribuciones de plazas"""
        
        async with AsyncSessionLocal() as session:
            # Obtener distribuciones activas para el lote
            query = select(DistribucionPlaza).where(
                and_(
                    DistribucionPlaza.lote == lote,
                    DistribucionPlaza.temporada == temporada,
                    DistribucionPlaza.activo == True,
                    DistribucionPlaza.plazas_establecidas > 0
                )
            )
            
            result = await session.execute(query)
            distribuciones = result.scalars().all()
            
            requirements = []
            for dist in distribuciones:
                requirement = TripRequirement(
                    lote=dist.lote,
                    provincia_origen=dist.provincia_origen,
                    zona_destino=dist.zona_destino,
                    dias_turno=dist.dias_turno,
                    tipo_turno=dist.tipo_turno,
                    plazas_requeridas=dist.plazas_establecidas,
                    temporada=temporada
                )
                requirements.append(requirement)
            
            return requirements
    
    async def _generate_optimized_trip(self, requirement: TripRequirement, num_reg: int) -> Optional[OptimizedTrip]:
        """Generar un viaje optimizado para un requisito específico"""
        
        try:
            # 1. Encontrar hotel disponible en la zona de destino
            hotel = await self._find_optimal_hotel(requirement)
            if not hotel:
                logger.warning(f"⚠️ No hay hoteles disponibles en {requirement.zona_destino}")
                return None
            
            # 2. Solo generar transportes si es con transporte (T)
            transportes_ida = []
            transportes_vuelta = []
            precio_total = 0.0
            km_total_ida = 0
            km_total_vuelta = 0
            
            if requirement.tipo_turno == 'T':
                # 3. Optimizar ruta de ida
                transportes_ida = await self._optimize_route(
                    requirement.provincia_origen, 
                    hotel.provincia_codigo,
                    requirement.lote,
                    'ida'
                )
                
                # 4. Optimizar ruta de vuelta
                transportes_vuelta = await self._optimize_route(
                    hotel.provincia_codigo,
                    requirement.provincia_origen, 
                    requirement.lote,
                    'vuelta'
                )
                
                if not transportes_ida or not transportes_vuelta:
                    logger.warning(f"⚠️ No se pudieron optimizar las rutas para {requirement.provincia_origen} <-> {hotel.provincia_codigo}")
                    return None
                
                # 5. Calcular totales
                precio_total = sum(t.precio for t in transportes_ida + transportes_vuelta)
                km_total_ida = sum(t.distancia_km for t in transportes_ida)
                km_total_vuelta = sum(t.distancia_km for t in transportes_vuelta)
            
            # 6. Determinar servicios en ruta
            servicios_ruta = await self._determine_route_services(transportes_ida, transportes_vuelta)
            
            # 7. Crear viaje optimizado
            optimized_trip = OptimizedTrip(
                requirement=requirement,
                hotel=hotel,
                transportes_ida=transportes_ida,
                transportes_vuelta=transportes_vuelta,
                precio_total=precio_total,
                km_total_ida=km_total_ida,
                km_total_vuelta=km_total_vuelta,
                servicios_ruta=servicios_ruta,
                num_reg=num_reg
            )
            
            # 8. Validar viaje generado
            is_valid = await self._validate_generated_trip(optimized_trip)
            if not is_valid:
                logger.warning(f"⚠️ Viaje generado no cumple validaciones: {num_reg}")
                return None
            
            return optimized_trip
            
        except Exception as e:
            logger.error(f"❌ Error generando viaje optimizado: {e}")
            return None
    
    async def _find_optimal_hotel(self, requirement: TripRequirement) -> Optional[Hotel]:
        """Encontrar el hotel óptimo para el requisito"""
        
        async with AsyncSessionLocal() as session:
            # Buscar hoteles en la zona de destino con capacidad suficiente
            query = select(Hotel).where(
                and_(
                    Hotel.zona_destino == requirement.zona_destino,
                    Hotel.activo == True,
                    Hotel.total_plazas >= requirement.plazas_requeridas
                )
            ).order_by(
                # Priorizar por capacidad y luego por código (consistencia)
                Hotel.total_plazas.desc(),
                Hotel.codigo
            )
            
            result = await session.execute(query)
            hoteles = result.scalars().all()
            
            # Verificar disponibilidad real
            for hotel in hoteles:
                disponible = await self._check_hotel_availability(
                    session, hotel, requirement.plazas_requeridas, requirement.temporada
                )
                if disponible:
                    return hotel
            
            return None
    
    async def _check_hotel_availability(self, session: AsyncSession, hotel: Hotel, 
                                      plazas_requeridas: int, temporada: str) -> bool:
        """Verificar disponibilidad real del hotel"""
        
        # Buscar disponibilidad específica
        query = select(HotelDisponibilidad).where(
            and_(
                HotelDisponibilidad.hotel_codigo == hotel.codigo,
                HotelDisponibilidad.temporada == temporada,
                HotelDisponibilidad.plazas_disponibles >= plazas_requeridas
            )
        )
        
        result = await session.execute(query)
        disponibilidad = result.scalar_one_or_none()
        
        # Si no hay registro específico, usar capacidad total del hotel
        return disponibilidad is not None or hotel.total_plazas >= plazas_requeridas
    
    async def _optimize_route(self, origen: str, destino: str, lote: str, direccion: str) -> List[TransportOption]:
        """Optimizar ruta entre origen y destino"""
        
        # Usar el optimizador de rutas para encontrar la mejor combinación
        route_options = await self.route_optimizer.find_optimal_route(
            origen=origen,
            destino=destino,
            lote=lote,
            max_transportes=self.max_transportes_por_direccion
        )
        
        if not route_options:
            # Si no hay rutas optimizadas, crear ruta directa en autocar
            return await self._create_direct_bus_route(origen, destino, direccion)
        
        return route_options
    
    async def _create_direct_bus_route(self, origen: str, destino: str, direccion: str) -> List[TransportOption]:
        """Crear ruta directa en autocar como fallback"""
        
        # Calcular distancia y tiempo estimado
        distancia = await self.route_optimizer.calculate_distance(origen, destino)
        
        # Horarios por defecto
        if direccion == 'ida':
            hora_salida = time(8, 0)
            hora_llegada = time(min(22, 8 + (distancia // 80) + 1), 0)  # ~80km/h promedio
        else:
            hora_salida = time(9, 0)
            hora_llegada = time(min(22, 9 + (distancia // 80) + 1), 0)
        
        # Precio estimado por kilómetro
        precio_por_km = 0.15  # €0.15 por km por persona
        precio_total = distancia * precio_por_km
        
        transport_option = TransportOption(
            tipo='A',  # Autocar
            origen=origen,
            destino=destino,
            hora_salida=hora_salida,
            hora_llegada=hora_llegada,
            precio=precio_total,
            distancia_km=distancia
        )
        
        return [transport_option]

    async def _determine_route_services(self, transportes_ida: List[TransportOption],
                                      transportes_vuelta: List[TransportOption]) -> Dict[str, str]:
        """Determinar servicios necesarios en ruta según horarios"""

        servicios = {}

        # Reglas para servicios en ruta (basadas en validaciones del sistema actual)
        if transportes_ida:
            primer_transporte = transportes_ida[0]
            ultimo_transporte = transportes_ida[-1]

            # Servicio en ida
            if (primer_transporte.hora_salida < time(14, 0) and ultimo_transporte.hora_llegada > time(15, 1)) or \
               (primer_transporte.hora_salida > time(15, 1) and ultimo_transporte.hora_llegada > time(22, 1)):
                servicios['ida'] = 'A'  # Almuerzo

        if transportes_vuelta:
            primer_transporte = transportes_vuelta[0]
            ultimo_transporte = transportes_vuelta[-1]

            # Servicio en vuelta
            if (primer_transporte.hora_salida < time(14, 0) and ultimo_transporte.hora_llegada > time(15, 1)) or \
               (primer_transporte.hora_salida > time(15, 1) and ultimo_transporte.hora_llegada > time(22, 1)):
                servicios['vuelta'] = 'A'  # Almuerzo

        return servicios

    async def _validate_generated_trip(self, trip: OptimizedTrip) -> bool:
        """Validar que el viaje generado cumple todas las reglas IMSERSO"""

        try:
            # 1. Validar transportes
            if trip.requirement.tipo_turno == 'T':
                # Primer transporte de vuelta debe ser autocar
                if trip.transportes_vuelta and trip.transportes_vuelta[0].tipo != 'A':
                    logger.warning(f"❌ Primer transporte vuelta no es autocar: {trip.transportes_vuelta[0].tipo}")
                    return False

                # Último transporte de ida debe ser autocar
                if trip.transportes_ida and trip.transportes_ida[-1].tipo != 'A':
                    logger.warning(f"❌ Último transporte ida no es autocar: {trip.transportes_ida[-1].tipo}")
                    return False

                # Límite de kilómetros para solo autocar
                solo_autocares_ida = all(t.tipo == 'A' for t in trip.transportes_ida)
                solo_autocares_vuelta = all(t.tipo == 'A' for t in trip.transportes_vuelta)

                if solo_autocares_ida and trip.km_total_ida > 499:
                    logger.warning(f"❌ Excede límite km ida solo autocar: {trip.km_total_ida}")
                    return False

                if solo_autocares_vuelta and trip.km_total_vuelta > 499:
                    logger.warning(f"❌ Excede límite km vuelta solo autocar: {trip.km_total_vuelta}")
                    return False

            # 2. Validar horarios
            hora_minima = self.hora_minima_salida.get(trip.requirement.lote, time(6, 0))

            if trip.transportes_ida:
                primer_hora = trip.transportes_ida[0].hora_salida
                if primer_hora and primer_hora < hora_minima:
                    logger.warning(f"❌ Hora inicio ida muy temprana: {primer_hora}")
                    return False

                ultima_hora = trip.transportes_ida[-1].hora_llegada
                if ultima_hora and ultima_hora > self.hora_maxima_llegada:
                    # Solo validar si hay un solo transporte
                    if len(trip.transportes_ida) == 1:
                        logger.warning(f"❌ Hora fin ida muy tarde: {ultima_hora}")
                        return False

            # 3. Validar capacidad de hotel
            if trip.hotel.total_plazas < trip.requirement.plazas_requeridas:
                logger.warning(f"❌ Hotel sin capacidad suficiente: {trip.hotel.total_plazas} < {trip.requirement.plazas_requeridas}")
                return False

            # 4. Validar secuencia de horarios
            if not self._validate_time_sequence(trip.transportes_ida):
                logger.warning(f"❌ Secuencia de horarios ida inválida")
                return False

            if not self._validate_time_sequence(trip.transportes_vuelta):
                logger.warning(f"❌ Secuencia de horarios vuelta inválida")
                return False

            return True

        except Exception as e:
            logger.error(f"❌ Error validando viaje: {e}")
            return False

    def _validate_time_sequence(self, transportes: List[TransportOption]) -> bool:
        """Validar que la secuencia de horarios es correcta"""

        if len(transportes) <= 1:
            return True

        for i in range(len(transportes) - 1):
            current = transportes[i]
            next_transport = transportes[i + 1]

            if current.hora_llegada and next_transport.hora_salida:
                if next_transport.hora_salida <= current.hora_llegada:
                    return False

        return True

    async def save_generated_trips(self, optimized_trips: List[OptimizedTrip]) -> List[str]:
        """Guardar viajes generados en la base de datos"""

        logger.info(f"💾 Guardando {len(optimized_trips)} viajes en base de datos...")

        saved_ids = []

        async with AsyncSessionLocal() as session:
            try:
                for trip in optimized_trips:
                    # Crear registro de viaje
                    viaje = Viaje(
                        num_reg=trip.num_reg,
                        lote=trip.requirement.lote,
                        temporada=trip.requirement.temporada,
                        tipo_turno=trip.requirement.tipo_turno,
                        zona_destino=trip.requirement.zona_destino,
                        provincia_hotel=trip.hotel.provincia_codigo,
                        dias_turno=trip.requirement.dias_turno,
                        provincia_origen=trip.requirement.provincia_origen,
                        num_total_plazas=trip.requirement.plazas_requeridas,

                        # Hotel
                        hotel_destino_1=trip.hotel.codigo,
                        hotel_destino_2=None,  # Según documentación debe estar en blanco

                        # Transportes IDA (hasta 4)
                        trans_1_ida=trip.transportes_ida[0].tipo if len(trip.transportes_ida) > 0 else None,
                        trans_2_ida=trip.transportes_ida[1].tipo if len(trip.transportes_ida) > 1 else None,
                        trans_3_ida=trip.transportes_ida[2].tipo if len(trip.transportes_ida) > 2 else None,
                        trans_4_ida=trip.transportes_ida[3].tipo if len(trip.transportes_ida) > 3 else None,

                        # Transportes VUELTA
                        trans_1_vuelta=trip.transportes_vuelta[0].tipo if len(trip.transportes_vuelta) > 0 else None,
                        trans_2_vuelta=trip.transportes_vuelta[1].tipo if len(trip.transportes_vuelta) > 1 else None,
                        trans_3_vuelta=trip.transportes_vuelta[2].tipo if len(trip.transportes_vuelta) > 2 else None,
                        trans_4_vuelta=trip.transportes_vuelta[3].tipo if len(trip.transportes_vuelta) > 3 else None,

                        # Aeropuertos (solo para vuelos)
                        aero_1_ida=self._extract_airport_code(trip.transportes_ida, 0),
                        aero_2_ida=self._extract_airport_code(trip.transportes_ida, 1),
                        aero_3_ida=self._extract_airport_code(trip.transportes_ida, 2),
                        aero_4_ida=self._extract_airport_code(trip.transportes_ida, 3),

                        # Horarios IDA
                        hora_inicio_ida=trip.transportes_ida[0].hora_salida if len(trip.transportes_ida) > 0 else None,
                        hora_inicio_2_ida=trip.transportes_ida[1].hora_salida if len(trip.transportes_ida) > 1 else None,
                        hora_inicio_3_ida=trip.transportes_ida[2].hora_salida if len(trip.transportes_ida) > 2 else None,
                        hora_inicio_4_ida=trip.transportes_ida[3].hora_salida if len(trip.transportes_ida) > 3 else None,
                        hora_fin_ida=trip.transportes_ida[-1].hora_llegada if trip.transportes_ida else None,

                        # Horarios VUELTA
                        hora_inicio_vuelta=trip.transportes_vuelta[0].hora_salida if len(trip.transportes_vuelta) > 0 else None,
                        hora_inicio_2_vuelta=trip.transportes_vuelta[1].hora_salida if len(trip.transportes_vuelta) > 1 else None,
                        hora_inicio_3_vuelta=trip.transportes_vuelta[2].hora_salida if len(trip.transportes_vuelta) > 2 else None,
                        hora_inicio_4_vuelta=trip.transportes_vuelta[3].hora_salida if len(trip.transportes_vuelta) > 3 else None,
                        hora_fin_vuelta=trip.transportes_vuelta[-1].hora_llegada if trip.transportes_vuelta else None,

                        # Kilómetros
                        total_km_ida=trip.km_total_ida,
                        total_km_vuelta=trip.km_total_vuelta,

                        # Servicios
                        serv_1_ruta_ida=trip.servicios_ruta.get('ida'),
                        serv_1_ruta_vuelta=trip.servicios_ruta.get('vuelta'),

                        # Otros
                        viaje_combinado='N',
                        estado='generado'
                    )

                    session.add(viaje)
                    await session.flush()  # Para obtener el ID
                    saved_ids.append(str(viaje.id))

                await session.commit()
                logger.info(f"✅ {len(saved_ids)} viajes guardados correctamente")

            except Exception as e:
                await session.rollback()
                logger.error(f"❌ Error guardando viajes: {e}")
                raise TripGenerationError(f"Error guardando viajes: {str(e)}")

        return saved_ids

    def _extract_airport_code(self, transportes: List[TransportOption], index: int) -> Optional[str]:
        """Extraer código de aeropuerto si el transporte es vuelo"""

        if index >= len(transportes):
            return None

        transport = transportes[index]
        if transport.tipo == 'V':
            # Para vuelos, el origen/destino debería ser el código IATA
            return transport.destino if transport.destino and len(transport.destino) == 3 else None

        return None

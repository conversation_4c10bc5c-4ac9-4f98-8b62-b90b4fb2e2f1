"""
Configuración de la aplicación
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, PostgresDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Configuración de la aplicación"""
    
    # Configuración básica
    PROJECT_NAME: str = "Sistema IMSERSO"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    
    # Configuración de la API
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 días
    
    # Configuración de CORS
    CORS_ORIGINS: List[AnyHttpUrl] = []
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Configuración de base de datos
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "imserso_user"
    POSTGRES_PASSWORD: str = "imserso_pass"
    POSTGRES_DB: str = "imserso"
    POSTGRES_PORT: str = "5432"
    DATABASE_URL: Optional[PostgresDsn] = None
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            port=values.get("POSTGRES_PORT"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # Configuración de Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Configuración de APIs de IA
    GEMINI_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    
    # Configuración de archivos
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS: List[str] = ["pdf", "xlsx", "xls", "csv", "txt"]
    UPLOAD_DIR: str = "storage/uploads"
    EXPORT_DIR: str = "storage/exports"
    TEMP_DIR: str = "storage/temp"
    
    # Configuración de email
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[EmailStr] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    @validator("EMAILS_FROM_NAME")
    def get_project_name(cls, v: Optional[str], values: Dict[str, Any]) -> str:
        if not v:
            return values["PROJECT_NAME"]
        return v
    
    # Configuración de logs
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/imserso.log"
    
    # Configuración de seguridad
    BCRYPT_ROUNDS: int = 12
    
    # APIs externas
    GOOGLE_MAPS_API_KEY: Optional[str] = None
    MAPBOX_API_KEY: Optional[str] = None
    ROUTE_OPTIMIZATION_API_URL: Optional[str] = None

    # APIs de mapas para cálculo de distancias reales (gratuitas)
    OPENROUTE_API_KEY: Optional[str] = None  # https://openrouteservice.org/
    GRAPHHOPPER_API_KEY: Optional[str] = None  # https://www.graphhopper.com/
    
    # Configuración de validaciones
    VALIDATION_RULES_CACHE_TTL: int = 3600  # 1 hora
    MAX_CONCURRENT_VALIDATIONS: int = 10
    
    # Configuración de generación de viajes
    MAX_TRIPS_PER_BATCH: int = 1000
    TRIP_GENERATION_TIMEOUT: int = 300  # 5 minutos
    
    # Configuración de exportación
    MAX_EXPORT_RECORDS: int = 100000
    EXPORT_CACHE_TTL: int = 1800  # 30 minutos
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Instancia global de configuración
settings = Settings()


# Configuración de logging
import logging
from loguru import logger
import sys

# Configurar loguru
logger.remove()  # Remover handler por defecto

# Handler para consola
logger.add(
    sys.stdout,
    level=settings.LOG_LEVEL,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    colorize=True,
)

# Handler para archivo (solo en producción)
if settings.ENVIRONMENT == "production":
    logger.add(
        settings.LOG_FILE,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="gz",
    )

# Configurar logging estándar de Python para que use loguru
class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# Interceptar logs de uvicorn, sqlalchemy, etc.
logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

"""Endpoints para gestión de hoteles"""
import os
import tempfile
from typing import Any, Optional
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from app.core.database import get_db
from app.services.data_import_service import DataImportService
from app.models.hotel import Hotel

router = APIRouter()

@router.post("/upload")
async def upload_hoteles(
    file: UploadFile = File(...),
    action: Optional[str] = Query(None, description="Acción: 'overwrite', 'append', o None para preguntar"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Subir archivo de hoteles (Anexo 11C)

    Si ya existen hoteles y no se especifica acción, devuelve opciones para confirmar.
    """

    # Validar formato de archivo
    if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
        raise HTTPException(status_code=400, detail="Formato de archivo no soportado. Use .xlsx, .xls o .csv")

    # Guardar archivo temporalmente
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
        content = await file.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name

    try:
        # Procesar importación
        import_service = DataImportService()
        result = await import_service.handle_file_import(
            file_path=tmp_file_path,
            import_type='hoteles',
            action=action or 'ask'
        )

        return {
            "filename": file.filename,
            "size": len(content),
            **result
        }

    finally:
        # Limpiar archivo temporal
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)

@router.post("/upload/confirm")
async def confirm_upload_hoteles(
    file: UploadFile = File(...),
    action: str = Query(..., description="Acción confirmada: 'overwrite' o 'append'"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Confirmar importación de hoteles con acción específica"""
    return await upload_hoteles(file=file, action=action, db=db)

@router.get("/")
async def list_hoteles(
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    zona_destino: Optional[str] = Query(None),
    provincia: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Listar hoteles con filtros opcionales"""

    query = select(Hotel).where(Hotel.activo == True)

    if zona_destino:
        query = query.where(Hotel.zona_destino == zona_destino)
    if provincia:
        query = query.where(Hotel.provincia_codigo == provincia)

    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    hoteles = result.scalars().all()

    # Contar total
    count_query = select(func.count(Hotel.codigo)).where(Hotel.activo == True)
    if zona_destino:
        count_query = count_query.where(Hotel.zona_destino == zona_destino)
    if provincia:
        count_query = count_query.where(Hotel.provincia_codigo == provincia)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    return {
        "hoteles": [
            {
                "codigo": hotel.codigo,
                "nombre": hotel.nombre,
                "municipio": hotel.municipio_codigo,
                "provincia": hotel.provincia_codigo,
                "zona_destino": hotel.zona_destino,
                "categoria": hotel.categoria,
                "total_plazas": hotel.total_plazas,
                "total_habitaciones": hotel.total_habitaciones,
            }
            for hotel in hoteles
        ],
        "total": total,
        "limit": limit,
        "offset": offset
    }

@router.get("/{hotel_codigo}")
async def get_hotel(hotel_codigo: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Obtener detalles de un hotel específico"""

    result = await db.execute(
        select(Hotel).where(Hotel.codigo == hotel_codigo)
    )
    hotel = result.scalar_one_or_none()

    if not hotel:
        raise HTTPException(status_code=404, detail=f"Hotel {hotel_codigo} no encontrado")

    return {
        "codigo": hotel.codigo,
        "nombre": hotel.nombre,
        "direccion": hotel.direccion,
        "municipio": hotel.municipio_codigo,
        "provincia": hotel.provincia_codigo,
        "zona_destino": hotel.zona_destino,
        "categoria": hotel.categoria,
        "total_plazas": hotel.total_plazas,
        "total_habitaciones": hotel.total_habitaciones,
        "servicios": hotel.servicios,
        "activo": hotel.activo,
        "created_at": hotel.created_at,
        "updated_at": hotel.updated_at
    }

@router.delete("/{hotel_codigo}")
async def delete_hotel(hotel_codigo: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Eliminar (desactivar) un hotel"""

    result = await db.execute(
        select(Hotel).where(Hotel.codigo == hotel_codigo)
    )
    hotel = result.scalar_one_or_none()

    if not hotel:
        raise HTTPException(status_code=404, detail=f"Hotel {hotel_codigo} no encontrado")

    hotel.activo = False
    await db.commit()

    return {"message": f"Hotel {hotel_codigo} desactivado correctamente"}

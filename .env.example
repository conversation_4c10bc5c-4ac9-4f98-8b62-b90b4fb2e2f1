# Base de datos PostgreSQL
POSTGRES_DB=imserso
POSTGRES_USER=imserso_user
POSTGRES_PASSWORD=your_secure_password_here

# Redis
REDIS_PASSWORD=your_redis_password_here

# Backend
SECRET_KEY=your-very-secure-secret-key-here-min-32-chars
ENVIRONMENT=production
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# APIs de IA
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Frontend
VITE_API_URL=http://localhost:8000
VITE_APP_TITLE=Sistema IMSERSO

# MinIO (Almacenamiento de archivos)
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=your_minio_password_here

# APIs externas
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
MAPBOX_API_KEY=your_mapbox_api_key_here

# Email (para notificaciones)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# Configuración de archivos
MAX_FILE_SIZE=50MB
ALLOWED_EXTENSIONS=pdf,xlsx,xls,csv

# Configuración de logs
LOG_LEVEL=INFO
LOG_FILE=logs/imserso.log

# Configuración de seguridad
JWT_EXPIRE_MINUTES=60
BCRYPT_ROUNDS=12

# URLs de servicios externos
ROUTE_OPTIMIZATION_API_URL=https://api.openrouteservice.org
HOTEL_VALIDATION_API_URL=https://api.example.com

"""
Modelos de hoteles
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, Text, ForeignKey, Date, DECIMAL
from sqlalchemy.dialects.postgresql import JSONB, POINT
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, CodedModel


class Hotel(CodedModel):
    """Modelo de hotel"""
    
    __tablename__ = "hoteles"
    
    nombre = Column(String(255), nullable=False, index=True)
    direccion = Column(Text)
    municipio_codigo = Column(String(20), ForeignKey("municipios.codigo"))
    provincia_codigo = Column(String(10), ForeignKey("provincias.codigo"))
    zona_destino = Column(String(10), ForeignKey("zonas_destino.codigo"))
    categoria = Column(String(10))
    total_habitaciones = Column(Integer)
    total_plazas = Column(Integer)
    servicios = Column(JSONB)
    coordenadas = Column(POINT)
    activo = Column(Boolean, default=True)
    
    # Relaciones
    municipio = relationship("Municipio", back_populates="hoteles")
    provincia = relationship("Provincia", back_populates="hoteles")
    zona_destino_rel = relationship("ZonaDestino", back_populates="hoteles")
    disponibilidad = relationship("HotelDisponibilidad", back_populates="hotel")
    viajes_destino_1 = relationship("Viaje", foreign_keys="Viaje.hotel_destino_1", back_populates="hotel_destino_1_rel")
    viajes_destino_2 = relationship("Viaje", foreign_keys="Viaje.hotel_destino_2", back_populates="hotel_destino_2_rel")


class HotelDisponibilidad(BaseModel):
    """Modelo de disponibilidad y precios de hoteles"""
    
    __tablename__ = "hotel_disponibilidad"
    
    hotel_codigo = Column(String(20), ForeignKey("hoteles.codigo"))
    fecha_inicio = Column(Date, nullable=False)
    fecha_fin = Column(Date, nullable=False)
    plazas_disponibles = Column(Integer, nullable=False)
    precio_por_plaza = Column(DECIMAL(10, 2))
    temporada = Column(String(10))
    observaciones = Column(Text)
    
    # Relaciones
    hotel = relationship("Hotel", back_populates="disponibilidad")

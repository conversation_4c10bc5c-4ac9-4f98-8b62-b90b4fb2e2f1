# Sistema IMSERSO

Sistema completo para la gestión y validación de viajes del IMSERSO, reemplazando el sistema actual basado en Google Sheets.

## 🚀 Características Principales

- **Procesamiento de Pliegos con IA**: Extracción automática de requisitos de PDFs usando Gemini/OpenAI
- **Gestión de Hoteles**: Importación y gestión de alojamientos con disponibilidad y precios
- **Gestión de Transportes**: Vuelos, trenes, AVE y autocares con optimización de rutas
- **Generación Automática de Viajes**: Creación de viajes cumpliendo todas las validaciones del IMSERSO
- **Motor de Validaciones**: Implementación completa de todas las reglas de negocio
- **Exportación Múltiple**: Descarga en formatos CSV, Excel y TXT
- **API REST Completa**: Backend FastAPI con documentación automática
- **Interfaz Web Moderna**: Frontend React con TypeScript

## 🏗️ Arquitectura

### Stack Tecnológico

- **Backend**: FastAPI (Python 3.11+)
- **Base de Datos**: PostgreSQL 15+
- **Frontend**: React 18+ con TypeScript
- **Cache**: Redis
- **Proxy**: Nginx
- **Contenedores**: Docker & Docker Compose
- **IA**: Google Gemini, OpenAI GPT-4, Anthropic Claude

### Estructura del Proyecto

```
imserso-system/
├── backend/          # API FastAPI
├── frontend/         # Aplicación React
├── database/         # Scripts SQL y migraciones
├── nginx/           # Configuración proxy
├── docs/            # Documentación
└── storage/         # Almacenamiento de archivos
```

## 🚀 Inicio Rápido

### Prerrequisitos

- Docker y Docker Compose
- Git

### Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd imserso-system
   ```

2. **Configurar variables de entorno**
   ```bash
   cp .env.example .env
   # Editar .env con tus configuraciones
   ```

3. **Levantar el sistema**
   ```bash
   # Producción
   docker-compose up -d
   
   # Desarrollo
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **Acceder a la aplicación**
   - Frontend: http://localhost:3000
   - API Docs: http://localhost:8000/docs
   - Base de datos: localhost:5432

## 📋 Validaciones Implementadas

El sistema implementa todas las validaciones del sistema Google Sheets actual:

### Validaciones de Transporte
- ✅ Primer transporte de vuelta debe ser autocar
- ✅ Último transporte de ida debe ser autocar
- ✅ Límite de 499km para solo autocares
- ✅ Uso obligatorio de todos los aeropuertos

### Validaciones de Horarios
- ✅ Horarios mínimos y máximos de salida
- ✅ Secuencia correcta de horarios
- ✅ Servicios obligatorios según horarios

### Validaciones de Hoteles
- ✅ Disponibilidad de habitaciones
- ✅ Capacidad suficiente para plazas solicitadas

### Validaciones de Distribución
- ✅ Coincidencia con plazas establecidas
- ✅ Temporada única por hoja
- ✅ Distribuciones específicas por lote (L1, L2, L3)

## 🔧 Desarrollo

### Configuración de Desarrollo

```bash
# Levantar solo base de datos
docker-compose -f docker-compose.dev.yml up postgres redis -d

# Ejecutar backend localmente
cd backend
pip install -r requirements.txt -r requirements-dev.txt
uvicorn app.main:app --reload

# Ejecutar frontend localmente
cd frontend
npm install
npm run dev
```

### Testing

```bash
# Backend
cd backend
pytest

# Frontend
cd frontend
npm test
```

### Linting y Formateo

```bash
# Backend
cd backend
black .
isort .
flake8 .
mypy .

# Frontend
cd frontend
npm run lint
npm run format
```

## 📊 Base de Datos

### Tablas Principales

- **users**: Usuarios del sistema
- **pliegos**: Pliegos procesados con IA
- **hoteles**: Alojamientos y disponibilidad
- **vuelos/trenes**: Transportes disponibles
- **viajes**: Viajes generados
- **validaciones_viaje**: Resultados de validaciones
- **reglas_validacion**: Reglas configurables

### Migraciones

```bash
# Crear migración
cd backend
alembic revision --autogenerate -m "Descripción"

# Aplicar migraciones
alembic upgrade head
```

## 🔐 Seguridad

- Autenticación JWT
- Validación de entrada con Pydantic
- Rate limiting
- CORS configurado
- Logs de auditoría

## 📈 Monitoreo

- Logs estructurados con Loguru
- Métricas con Prometheus
- Health checks en todos los servicios

## 🚀 Despliegue

### Producción

```bash
# Configurar variables de entorno de producción
cp .env.example .env.prod

# Desplegar
docker-compose -f docker-compose.yml up -d

# Verificar estado
docker-compose ps
```

### Backup de Base de Datos

```bash
# Crear backup
docker-compose exec postgres pg_dump -U imserso_user imserso > backup.sql

# Restaurar backup
docker-compose exec -T postgres psql -U imserso_user imserso < backup.sql
```

## 📚 Documentación

- **API**: http://localhost:8000/docs (Swagger)
- **Redoc**: http://localhost:8000/redoc
- **Arquitectura**: [docs/arquitectura.md](docs/arquitectura.md)
- **Base de Datos**: [docs/database.md](docs/database.md)

## 🤝 Contribución

1. Fork del proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

Para soporte técnico:
- Crear issue en GitHub
- Email: <EMAIL>
- Documentación: [docs/](docs/)

---

**Desarrollado para modernizar y optimizar la gestión de viajes del IMSERSO** 🚌✈️🏨

# 🚀 Guía de Testing con Postman - Sistema IMSERSO

## 📁 Archivos Incluidos

1. **`IMSERSO_Postman_Collection.json`** - Colección completa de endpoints
2. **`IMSERSO_Environment.postman_environment.json`** - Variables de entorno
3. **`POSTMAN_TESTING_GUIDE.md`** - Esta guía

## 🔧 Configuración Inicial

### 1. Importar en Postman

1. Abre **Postman**
2. Click en **Import** (botón superior izquierdo)
3. Arrastra los archivos `.json` o selecciona **Upload Files**
4. Importa ambos archivos:
   - `IMSERSO_Postman_Collection.json`
   - `IMSERSO_Environment.postman_environment.json`

### 2. Configurar Environment

1. En Postman, selecciona el environment **"🚀 IMSERSO Development"**
2. Verifica que `base_url` esté configurado como `http://localhost:8000`
3. Las demás variables se configuran automáticamente

## 🎯 Flujo de Testing Recomendado

### Paso 1: Verificar Sistema
```
🏥 Health Check
```
- **Propósito**: Verificar que el sistema esté funcionando
- **Esperado**: Status "healthy" y database "connected"

### Paso 2: Autenticación
```
🔐 Login
```
- **Propósito**: Obtener token JWT para las demás operaciones
- **Automático**: El token se guarda automáticamente en el environment
- **Credenciales**: <EMAIL> / admin123

### Paso 3: Funcionalidad Principal
```
🚀 Generar Viajes (PRINCIPAL)
```
- **Propósito**: Generar viajes automáticamente con optimización
- **Parámetros**:
  - `lote`: "L1"
  - `temporada`: "2025"
  - `optimize_routes`: true
  - `include_transfers`: true

### Paso 4: Validación
```
✅ Validar Lote
```
- **Propósito**: Validar viajes con 47+ reglas IMSERSO
- **Incluye**: Validaciones de distancia, tiempo, disponibilidad, etc.

### Paso 5: Exportación
```
📤 Exportar Resultados
```
- **Propósito**: Generar archivos OUTCOME listos para entregar
- **Formatos**: Excel, CSV, PDF

## 📋 Endpoints Disponibles

### 🏥 Sistema
- **Health Check** - Estado del sistema
- **Dashboard Stats** - Métricas y estadísticas

### 🔐 Autenticación
- **Login** - Autenticación JWT
- **Register** - Crear usuarios

### ✈️ Viajes (PRINCIPAL)
- **Generar Viajes** - 🚀 Funcionalidad principal
- **Listar Viajes** - Ver viajes generados
- **Optimizar Rutas** - Re-optimización

### ✅ Validaciones
- **Validar Lote** - 🔍 47+ reglas IMSERSO
- **Reportes** - Informes detallados

### 📤 Exportación
- **Exportar** - Archivos OUTCOME
- **Descargar** - Múltiples formatos

### 🏨 Gestión de Datos
- **Importar Hoteles** - Desde Excel
- **Importar Transportes** - Vuelos, trenes, autocares
- **Procesar Pliegos** - PDFs con IA

## 🎯 Casos de Uso Principales

### 1. Flujo Completo de Generación
```
1. Health Check ✅
2. Login 🔐
3. Importar Hoteles 🏨
4. Importar Transportes 🚌
5. Generar Viajes 🚀
6. Validar Resultados ✅
7. Exportar Archivos 📤
```

### 2. Testing Rápido
```
1. Health Check ✅
2. Login 🔐
3. Generar Viajes 🚀
4. Listar Resultados 📋
```

### 3. Validación Intensiva
```
1. Login 🔐
2. Generar Viajes 🚀
3. Validar Lote ✅
4. Revisar Errores 🔍
5. Re-optimizar 🔄
```

## 🔍 Respuestas Esperadas

### Health Check
```json
{
  "status": "healthy",
  "timestamp": "2025-07-08T11:30:00.000Z",
  "database": {
    "status": "connected",
    "type": "PostgreSQL"
  }
}
```

### Login
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Generar Viajes
```json
{
  "success": true,
  "viajes_generados": 150,
  "tiempo_procesamiento": "45.2s",
  "optimizaciones_aplicadas": 23,
  "lote": "L1",
  "temporada": "2025"
}
```

## 🚨 Troubleshooting

### Error 401 - Unauthorized
- **Solución**: Ejecutar Login primero
- **Verificar**: Token en environment variables

### Error 500 - Internal Server Error
- **Verificar**: Sistema IMSERSO esté corriendo
- **Comando**: `docker-compose ps`

### Error de Conexión
- **Verificar**: `base_url` en environment
- **Debe ser**: `http://localhost:8000`

## 🎉 ¡Listo para Probar!

1. **Importa** los archivos en Postman
2. **Selecciona** el environment "🚀 IMSERSO Development"
3. **Ejecuta** Health Check para verificar
4. **Haz Login** para obtener token
5. **¡Prueba la generación automática de viajes!**

## 📞 Soporte

Si encuentras algún problema:
1. Verifica que el sistema esté corriendo: `docker-compose ps`
2. Revisa los logs: `docker-compose logs -f backend`
3. Verifica la documentación: http://localhost:8000/docs

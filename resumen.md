### **Resumen del Progreso del Diseño Técnico del Sistema de Gestión de Viajes del Imserso**

Este documento recopila las decisiones y diseños acordados hasta la fecha para el sistema de gestión de viajes del Imserso. Sirve como punto de partida para futuras discusiones y para asegurar la continuidad del proyecto.

#### **1\. Introducción y Objetivo General**

* **Objetivo Principal:** Desarrollar un sistema automatizado para la gestión, validación y generación de documentos relacionados con los programas de viajes del Imserso. El sistema deberá ser adaptable a los cambios anuales en los pliegos y normativas.

#### **2\. Tecnologías Seleccionadas**

Se ha acordado el siguiente stack tecnológico, priorizando la robustez, escalabilidad y facilidad de integración:

* **Backend:** Python con el framework **FastAPI**.  
* **Bibliotecas Python Clave:**  
  * pandas: Para manipulación de datos, especialmente durante la ingesta de CSV.  
  * PyPDF2 o pdfminer.six: Para la extracción de texto de documentos PDF.  
  * camelot-py o tabula-py: Para la extracción estructurada de tablas de documentos PDF.  
  * WeasyPrint: Para la generación de documentos PDF a partir de HTML/CSS (para anexos).  
  * python-docx: Para el relleno automático de plantillas de documentos Word (.docx) (para contratos de hotel).  
* **Base de Datos:** **PostgreSQL**.  
* **Frontend:** **React** (con Vite para el scaffolding del proyecto).  
* **Inteligencia Artificial (IA):** **Google Gemini API** (específicamente Gemini 1.5 Pro) para la extracción inteligente de información de los pliegos.  
* **Contenedores:** **Docker y Docker Compose** para la contenerización y orquestación de todos los servicios (Frontend, Backend, Base de Datos).

#### **3\. Arquitectura del Sistema (Visión General)**

El sistema se basará en una arquitectura de "monolito modular" con una clara separación de responsabilidades entre los componentes principales, todos ellos contenerizados:

* **Frontend (React):** Interfaz de usuario para la interacción con el sistema (formularios, visualizaciones, carga/descarga).  
* **Backend (FastAPI):** Lógica de negocio central, API REST, orquestación de procesos, gestión de autenticación/autorización.  
* **Servicio de Procesamiento de Documentos (Módulo del Backend):** Encargado de la pre-extracción de PDFs y la interacción con la API de Gemini para la extracción inteligente de datos.  
* **Base de Datos (PostgreSQL):** Almacenamiento persistente de todos los datos de la aplicación.  
* **Librerías de Generación de Documentos (Módulo del Backend):** Para la creación de PDFs y el relleno de plantillas Word.

#### **4\. Diseño de Base de Datos (Sección 6\)**

Se ha definido un esquema detallado para PostgreSQL, que incluye las siguientes entidades clave y sus relaciones:

* **USUARIOS**: Gestión de usuarios y roles (ADMIN, OPERADOR).  
* **PLIEGOS**: Almacena el contenido crudo del PDF del pliego, los datos extraídos por la IA (en formato JSONB para flexibilidad), y metadatos sobre el estado y la revisión del pliego.  
* **REQUISITOS\_PLIEGO**: Almacena requisitos generales extraídos del pliego que son clave para las validaciones.  
* **PLAZAS\_MINIMAS\_PLIEGO**: Detalla las plazas mínimas requeridas por combinación de pliego, provincia de origen, lote de destino y rangos de duración.  
* **PAISES, PROVINCIAS, MUNICIPIOS**: Tablas de datos maestros geográficos.  
* **AEROPUERTOS**: Datos maestros de aeropuertos (código IATA, nombre, ubicación).  
* **HOTELES**: Datos maestros de hoteles (nombre, categoría, dirección, ubicación).  
* **PRECIOS\_HOTEL\_PERIODO**: Almacena los precios de estancia por hotel, tipo de estancia y periodos de validez (según ANEXO 11C).  
* **PRECIOS\_TRANSPORTE\_PERIODO**: Almacena los precios de transporte por tipo, origen, destino y periodos.  
* **VIAJES\_PLANIFICADOS**: Contiene los datos de cada viaje individual (código, lote, fechas, origen, destino, plazas previstas), junto con su estado\_validacion y mensaje\_validacion.  
* **CONTRATOS\_HOTEL**: Registra los contratos de hotel generados, vinculados a un hotel y conteniendo los datos específicos utilizados para su creación.  
* **DEFINICION\_ANEXOS\_SALIDA**: **(CRÍTICO)** Esta tabla almacena dinámicamente la estructura y los nombres de las columnas que deben tener los ficheros de salida (Anexo 7 y Anexo 8\) para un pliego específico. Esto permite que el sistema se adapte a posibles cambios anuales en el formato de los anexos requeridos por el Imserso.

#### **5\. Diseño Detallado de Módulos (Sección 7 \- En Progreso)**

Se han detallado los siguientes módulos:

**5.1. Módulo de Extracción y Corrección de Pliegos:**

* **Flujo de Trabajo:** Permite al usuario subir un PDF de pliego. El sistema extrae texto y tablas, las envía a Gemini para una extracción inteligente de datos clave (plazas, condiciones, etc.). Los datos extraídos se almacenan en un estado PENDIENTE\_REVISION.  
* **Interfaz de Revisión y Corrección:** Se implementará una interfaz dedicada donde el usuario podrá visualizar los datos extraídos y corregir manualmente cualquier inconsistencia o error antes de "activar" el pliego para su uso en las validaciones. Esta interfaz también permitirá definir o ajustar la DEFINICION\_ANEXOS\_SALIDA para ese pliego.  
* **Interacción con Gemini API:** Se utilizarán prompts detallados con especificación de esquema JSON para la salida.

**5.2. Módulo de Carga Masiva y Gestión de Viajes:**

* **Carga Masiva CSV:** Permite la ingesta de datos de viajes desde archivos CSV. El sistema validará las cabeceras del CSV contra la DEFINICION\_ANEXOS\_SALIDA del pliego activo y realizará validaciones básicas por fila antes de insertar los datos en VIAJES\_PLANIFICADOS (con estado\_validacion inicial PENDIENTE). Se generará un informe de errores.  
* **Gestión CRUD de Viajes:** Interfaz para listar, crear, editar y eliminar viajes individualmente.  
* **Gestión de Precios:** Interfaces para introducir y gestionar los precios de hoteles (PRECIOS\_HOTEL\_PERIODO) y transporte (PRECIOS\_TRANSPORTE\_PERIODO) por periodos.

**5.3. Módulo de Validaciones de Viajes:**

* **Flujo de Validación:** Se activa la validación (manual o automática). El sistema recupera los datos del viaje y los requisitos del pliego activo.  
* **Reglas de Validación por Defecto (Inferidas de ANEXO 7, 8 y 11C):**  
  * **Formato de Fichero:** Validación de codificación UTF8, longitud fija, formato de números (ajuste a la derecha, ceros a la izquierda), formato de alfanuméricos (ajuste a la izquierda, espacios a la derecha), no decimales.  
  * **Campos de Viaje (ANEXO 7):**  
    * NumReg: Obligatorio, secuencial, único, numérico.  
    * Temporada: Obligatorio, aaaa.  
    * Tipo Turno: Obligatorio, 'T' o 'S'.  
    * Zona Dest: Obligatorio, código válido según tabla de zonas/modalidades.  
    * CodProvHotel: Obligatorio, código de provincia válido.  
    * Dias Turno: Obligatorio, numérico entero.  
    * Cod ProvOrig: Obligatorio, código de provincia/ciudad exterior válido.  
    * Num Total Plazas: Obligatorio, numérico.  
    * Horalniciolda: Obligatorio si Tipo Turno es 'T', vacío si es 'S'.  
    * CodHotel2Destino: Debe estar en blanco.  
    * Viaje Combinado: Debe ser 'N'.  
  * **Oferta Hotelera (ANEXO 8):** Unicidad de hotel por lote.  
  * **Servicios Hoteleros (ANEXO 11C):**  
    * **Disponibilidad de Servicios:** Habitaciones con baño, agua caliente, teléfono, TV, calefacción. Comedor con capacidad, salas de animación, acceso a Internet en zonas comunes, ascensores, accesibilidad en zonas comunes, sala de atención médica (excepto turismo interior), frigorífico para medicinas.  
    * **Limpieza:** Diaria de habitaciones, toallas diarias, ropa de cama semanal/necesaria.  
    * **Régimen de Comidas:** Pensión completa (excepto capitales de provincia: media pensión). Garantía de almuerzo/cena en llegadas tardías (con transporte).  
    * **Calidad de Menús:** Adaptados, abundantes, calóricos, opciones para intolerancias/dietas. Buffet opcional. Variedad en primeros, segundos y postres. Vino y agua incluidos. Equilibrio dietético. Prohibición de "picnic". Menús especiales en festivos con suplemento. Flexibilidad horaria.  
  * **Validación de Plazas Mínimas/Máximas:** Agregación de plazas por criterios de pliego y comparación con los requisitos.  
  * **Validación de Precios:** Comparación de precios combinados (hotel \+ transporte) con límites del pliego.  
  * **Adaptabilidad a Pliegos:** Si un nuevo pliego introduce variaciones en estas validaciones, el sistema **deberá preguntar al usuario si desea aplicar los nuevos criterios o mantener los preexistentes**.  
  * **Resultados:** El estado\_validacion (VALIDO/NO\_VALIDO) y mensaje\_validacion se actualizarán en VIAJES\_PLANIFICADOS.

#### **7.4. Módulo de Generación de Resultados y Documentos**

Este módulo será responsable de producir los entregables finales del sistema: los Anexos 7 y 8 en formato CSV y PDF, y los contratos de hotel rellenados automáticamente.

* **7.4.1. Generación de Anexos 7 y 8 (CSV)**  
  * **Flujo de Trabajo:**  
    1. **Usuario (Frontend):** Selecciona el pliego activo y el tipo de anexo a generar (ej. "Anexo 7 Lote 1 Outcome", "Anexo 8 Lote 2").  
    2. **Frontend (React):** Realiza una petición GET al Backend (ej. /api/anexos/{tipo\_anexo}/csv?pliego\_id={id\_pliego}).  
    3. **Backend (FastAPI):**  
       * Recupera los datos relevantes de VIAJES\_PLANIFICADOS de la Base de Datos, filtrando por el lote y pliego seleccionados, y preferiblemente solo los viajes con estado\_validacion \= 'VALIDO'.  
       * Consulta la tabla DEFINICION\_ANEXOS\_SALIDA para el pliego\_id dado y el nombre\_anexo correspondiente, para obtener la estructura de columnas (definicion\_columnas).  
       * Transforma los datos de los viajes a la estructura y el orden de columnas definidos en definicion\_columnas, utilizando los nombre\_mostrar como cabeceras.  
       * Genera el contenido del archivo CSV.  
       * Devuelve el CSV como una respuesta HTTP con el Content-Type: text/csv y el encabezado Content-Disposition para forzar la descarga.  
    4. **Usuario (Frontend):** El navegador descarga el archivo CSV.  
* **7.4.2. Generación de Anexos 7 y 8 (PDF para Visualización y Descarga)**  
  * **Flujo de Trabajo:**  
    1. **Usuario (Frontend):** Selecciona el pliego y el tipo de anexo, y elige "Ver PDF" o "Descargar PDF".  
    2. **Frontend (React):** Realiza una petición GET al Backend (ej. /api/anexos/{tipo\_anexo}/pdf?pliego\_id={id\_pliego}).  
    3. **Backend (FastAPI):**  
       * Recupera los datos de VIAJES\_PLANIFICADOS y la DEFINICION\_ANEXOS\_SALIDA como en el caso del CSV.  
       * **Generación de HTML:** Transforma los datos en una estructura HTML (\<table\> con estilos CSS) que represente el anexo de manera clara y legible. Se utilizarán plantillas HTML para mantener la consistencia visual. Los estilos CSS se diseñarán para asegurar una buena presentación en PDF (considerando paginación, márgenes, fuentes).  
       * **Generación de PDF:** Utiliza la librería WeasyPrint para convertir el HTML generado en un archivo PDF.  
       * Devuelve el PDF como una respuesta HTTP con el Content-Type: application/pdf y el encabezado Content-Disposition.  
    4. **Usuario (Frontend):** El navegador puede mostrar el PDF directamente o iniciar la descarga, según la configuración del navegador y el encabezado Content-Disposition.  
  * **Consideraciones de Diseño:**  
    * **Maquetación Responsiva para PDF:** Aunque es PDF, el HTML subyacente debe ser diseñado para que WeasyPrint lo renderice correctamente, manejando saltos de página y tamaños de tabla.  
    * **Estilos Imserso:** Se buscará replicar el estilo visual de los documentos oficiales del Imserso en la medida de lo posible para los PDFs.  
* **7.4.3. Generación Automatizada de Contratos de Hotel (Relleno de Plantilla)**  
  * **Flujo de Trabajo:**  
    1. **Usuario (Frontend):** Selecciona un hotel y una temporada/periodo para generar su contrato. Puede haber una interfaz para subir y gestionar las plantillas de contrato (.docx).  
    2. **Frontend (React):** Realiza una petición POST al Backend (ej. /api/contratos/generar, con hotel\_id y pliego\_id).  
    3. **Backend (FastAPI):**  
       * Recupera los datos del hotel (HOTELES), los precios por periodo (PRECIOS\_HOTEL\_PERIODO), y cualquier otra información relevante del PLIEGOS.datos\_extraidos o REQUISITOS\_PLIEGO necesaria para el contrato.  
       * **Carga de Plantilla:** Carga la plantilla de contrato de hotel (.docx) previamente subida y almacenada (ej. en un volumen Docker o un servicio de almacenamiento).  
       * **Relleno de Campos:** Utiliza la librería python-docx para identificar marcadores de posición en la plantilla (ej. {{NOMBRE\_HOTEL}}, {{FECHA\_INICIO\_CONTRATO}}, {{PRECIO\_PERNOCTACION}}) y reemplazarlos con los datos recuperados de la Base de Datos.  
       * **Guardado y Registro:** Guarda el nuevo archivo .docx generado en un almacenamiento persistente y registra la generación en la tabla CONTRATOS\_HOTEL (incluyendo datos\_contrato\_generado como JSONB para auditoría).  
       * Devuelve el documento .docx generado como una respuesta HTTP con el Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document y el encabezado Content-Disposition.  
    4. **Usuario (Frontend):** El navegador descarga el archivo .docx.  
  * **Consideraciones de Plantilla:**  
    * La plantilla .docx debe ser diseñada con marcadores de posición claros y consistentes para que python-docx pueda identificarlos y reemplazarlos.  
    * Se deberá definir un proceso para que el usuario pueda subir y gestionar las plantillas de contrato en el sistema.

#### **6\. Puntos Pendientes en la Documentación Técnica**

Para completar la documentación técnica del proyecto, aún necesitamos detallar las siguientes secciones:

1. **8\. Definición de APIs (Endpoints):** Especificación de todos los endpoints RESTful del Backend.  
2. **9\. Consideraciones de Seguridad:** Medidas de seguridad a implementar a nivel de aplicación y despliegue.  
3. **10\. Estrategia de Despliegue:** Detalles sobre la configuración de Docker y Docker Compose para los diferentes entornos (desarrollo, pruebas, producción).  
4. **11\. Plan de Pruebas:** Estrategias y tipos de pruebas a realizar para asegurar la calidad del software.  
5. **12\. Futuras Mejoras (Roadmap):** Un plan a largo plazo para posibles evoluciones del sistema.
"""
Servicio para importar datos del Google Sheets actual y manejar importaciones
"""

import asyncio
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, text, func
from loguru import logger

from app.core.database import AsyncSessionLocal
from app.core.exceptions import FileProcessingError, DatabaseError
from app.models.master_data import (
    Provincia, Municipio, Aeropuerto, ZonaDestino,
    TipoTransporte, TipoServicio, DistribucionPlaza
)
from app.models.hotel import Hotel, HotelDisponibilidad
from app.models.transport import Vuelo, Tren
from app.models.validation import ReglaValidacion


class DataImportService:
    """Servicio para importar y gestionar datos maestros"""
    
    def __init__(self):
        self.google_sheets_url = "https://docs.google.com/spreadsheets/d/1dXz4z4HthnzyTGbkP78YLm8HV2LCAcOtoBfqMMq5atE/export?format=xlsx"
        self.excel_file_path = "APPIM2025.xlsx"
    
    async def import_initial_data(self) -> Dict[str, int]:
        """
        Importar todos los datos iniciales del Google Sheets
        """
        logger.info("🔄 Iniciando importación de datos iniciales...")
        
        try:
            # Leer el archivo Excel
            excel_data = await self._read_excel_file()
            
            results = {}
            
            # Importar datos maestros en orden de dependencias
            results['provincias'] = await self._import_provincias(excel_data)
            results['municipios'] = await self._import_municipios(excel_data)
            results['aeropuertos'] = await self._import_aeropuertos(excel_data)
            results['zonas_destino'] = await self._import_zonas_destino(excel_data)
            results['tipos_transporte'] = await self._import_tipos_transporte(excel_data)
            results['tipos_servicio'] = await self._import_tipos_servicio(excel_data)
            
            # Importar datos de hoteles
            results['hoteles'] = await self._import_hoteles(excel_data)
            
            # Importar distribuciones de plazas
            results['distribuciones_l1_t'] = await self._import_distribuciones_l1_t(excel_data)
            results['distribuciones_l1_s'] = await self._import_distribuciones_l1_s(excel_data)
            results['distribuciones_l2_t'] = await self._import_distribuciones_l2_t(excel_data)
            results['distribuciones_l2_s'] = await self._import_distribuciones_l2_s(excel_data)
            results['distribuciones_l3'] = await self._import_distribuciones_l3(excel_data)
            
            # Importar reglas de validación
            results['reglas_validacion'] = await self._import_validation_rules(excel_data)
            
            logger.info(f"✅ Importación completada: {results}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error en importación inicial: {e}")
            raise DatabaseError(f"Error importando datos iniciales: {str(e)}")
    
    async def _read_excel_file(self) -> Dict[str, pd.DataFrame]:
        """Leer todas las hojas del archivo Excel"""
        try:
            # Intentar leer desde URL primero, luego archivo local
            try:
                excel_data = pd.read_excel(self.google_sheets_url, sheet_name=None, engine='openpyxl')
                logger.info("📊 Datos leídos desde Google Sheets")
            except:
                excel_data = pd.read_excel(self.excel_file_path, sheet_name=None, engine='openpyxl')
                logger.info("📊 Datos leídos desde archivo local")
            
            logger.info(f"📋 Hojas encontradas: {list(excel_data.keys())}")
            return excel_data
            
        except Exception as e:
            raise FileProcessingError(f"Error leyendo archivo Excel: {str(e)}", "APPIM2025.xlsx")
    
    async def _import_provincias(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar provincias desde la hoja 'Provincia'"""
        if 'Provincia' not in excel_data:
            logger.warning("⚠️ Hoja 'Provincia' no encontrada")
            return 0
        
        df = excel_data['Provincia']
        if df.empty:
            return 0
        
        # Limpiar y preparar datos
        df = df.dropna(subset=[df.columns[0], df.columns[1]])  # Código y nombre
        df.columns = ['codigo', 'nombre'] + list(df.columns[2:])
        
        async with AsyncSessionLocal() as session:
            try:
                # Limpiar datos existentes
                await session.execute(delete(Provincia))

                # Insertar nuevos datos
                count = 0
                for _, row in df.iterrows():
                    provincia = Provincia(
                        codigo=str(row['codigo']).zfill(2),
                        nombre=str(row['nombre']).strip(),
                        comunidad_autonoma=None  # Se puede mapear después
                    )
                    session.add(provincia)
                    count += 1
                
                await session.commit()
                logger.info(f"✅ Importadas {count} provincias")
                return count
                
            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando provincias: {str(e)}")
    
    async def _import_municipios(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar municipios desde la hoja 'Municipio'"""
        if 'Municipio' not in excel_data:
            logger.warning("⚠️ Hoja 'Municipio' no encontrada")
            return 0
        
        df = excel_data['Municipio']
        if df.empty:
            return 0
        
        # Preparar datos (ajustar según estructura real)
        df = df.dropna(subset=[df.columns[0]])  # Al menos código
        
        async with AsyncSessionLocal() as session:
            try:
                await session.execute(delete(municipios))
                
                count = 0
                for _, row in df.iterrows():
                    # Ajustar según estructura real de la hoja
                    municipio = municipios(
                        codigo=str(row.iloc[0]),
                        nombre=str(row.iloc[1]) if len(row) > 1 else "Sin nombre",
                        provincia_codigo=str(row.iloc[2]).zfill(2) if len(row) > 2 else None,
                        poblacion=None
                    )
                    session.add(municipio)
                    count += 1
                
                await session.commit()
                logger.info(f"✅ Importados {count} municipios")
                return count
                
            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando municipios: {str(e)}")
    
    async def _import_aeropuertos(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar aeropuertos desde la hoja 'IATA'"""
        if 'IATA' not in excel_data:
            logger.warning("⚠️ Hoja 'IATA' no encontrada")
            return 0
        
        df = excel_data['IATA']
        if df.empty:
            return 0
        
        # Limpiar datos
        df = df.dropna(subset=[df.columns[0]])  # Código IATA
        
        async with AsyncSessionLocal() as session:
            try:
                await session.execute(delete(aeropuertos))
                
                count = 0
                for _, row in df.iterrows():
                    codigo_iata = str(row.iloc[0]).strip().upper()
                    if len(codigo_iata) == 3:  # Validar código IATA
                        aeropuerto = aeropuertos(
                            codigo_iata=codigo_iata,
                            nombre=str(row.iloc[1]) if len(row) > 1 else f"Aeropuerto {codigo_iata}",
                            ciudad=str(row.iloc[2]) if len(row) > 2 else None,
                            pais="España",
                            activo=True
                        )
                        session.add(aeropuerto)
                        count += 1
                
                await session.commit()
                logger.info(f"✅ Importados {count} aeropuertos")
                return count
                
            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando aeropuertos: {str(e)}")
    
    async def _import_zonas_destino(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar zonas de destino desde la hoja 'Zona de Destino'"""
        if 'Zona de Destino' not in excel_data:
            logger.warning("⚠️ Hoja 'Zona de Destino' no encontrada")
            return 0
        
        df = excel_data['Zona de Destino']
        if df.empty:
            return 0
        
        async with AsyncSessionLocal() as session:
            try:
                await session.execute(delete(zonas_destino))
                
                count = 0
                for _, row in df.iterrows():
                    if pd.notna(row.iloc[0]):
                        zona = zonas_destino(
                            codigo=str(row.iloc[0]).strip(),
                            nombre=str(row.iloc[1]) if len(row) > 1 else str(row.iloc[0]),
                            descripcion=str(row.iloc[2]) if len(row) > 2 else None,
                            activa=True
                        )
                        session.add(zona)
                        count += 1
                
                await session.commit()
                logger.info(f"✅ Importadas {count} zonas de destino")
                return count
                
            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando zonas de destino: {str(e)}")
    
    async def _import_tipos_transporte(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar tipos de transporte desde la hoja 'Tipo Transporte'"""
        if 'Tipo Transporte' not in excel_data:
            # Usar datos por defecto
            tipos_default = [
                ('A', 'Autocar', 'Transporte por carretera'),
                ('V', 'Avión', 'Transporte aéreo'),
                ('T', 'Tren', 'Transporte ferroviario'),
                ('E', 'AVE', 'Tren de alta velocidad'),
            ]

            async with AsyncSessionLocal() as session:
                try:
                    await session.execute(delete(tipos_transporte))

                    for codigo, nombre, desc in tipos_default:
                        tipo = tipos_transporte(
                            codigo=codigo,
                            nombre=nombre,
                            descripcion=desc,
                            activo=True
                        )
                        session.add(tipo)

                    await session.commit()
                    logger.info(f"✅ Importados {len(tipos_default)} tipos de transporte (por defecto)")
                    return len(tipos_default)

                except Exception as e:
                    await session.rollback()
                    raise DatabaseError(f"Error importando tipos de transporte: {str(e)}")

        # TODO: Implementar lectura desde hoja si existe
        return 0

    async def _import_tipos_servicio(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar tipos de servicio en ruta"""
        servicios_default = [
            ('D', 'Desayuno', 'Servicio de desayuno en ruta'),
            ('A', 'Almuerzo', 'Servicio de almuerzo en ruta'),
            ('C', 'Cena', 'Servicio de cena en ruta'),
            ('M', 'Merienda', 'Servicio de merienda en ruta'),
            ('R', 'Refrigerio', 'Servicio de refrigerio en ruta'),
        ]

        async with AsyncSessionLocal() as session:
            try:
                await session.execute(delete(tipos_servicio))

                for codigo, nombre, desc in servicios_default:
                    servicio = tipos_servicio(
                        codigo=codigo,
                        nombre=nombre,
                        descripcion=desc,
                        activo=True
                    )
                    session.add(servicio)

                await session.commit()
                logger.info(f"✅ Importados {len(servicios_default)} tipos de servicio")
                return len(servicios_default)

            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando tipos de servicio: {str(e)}")

    async def _import_hoteles(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar hoteles desde las hojas L1-Anexo8, L2-Anexo8, L3-Anexo8"""
        total_count = 0

        for lote in ['L1', 'L2', 'L3']:
            sheet_name = f'{lote}-Anexo8'
            if sheet_name not in excel_data:
                logger.warning(f"⚠️ Hoja '{sheet_name}' no encontrada")
                continue

            df = excel_data[sheet_name]
            if df.empty:
                continue

            # Mapear columnas según estructura del Anexo8
            column_mapping = {
                'CodHotel': 'codigo',
                'DenHotel': 'nombre',
                'NomMunHotel': 'municipio',
                'ProvHotel': 'provincia_codigo',
                'ZonaDest': 'zona_destino',
                'CatHotel': 'categoria',
                'NumPlazasOfert': 'total_plazas',
                'NumEstancOfert': 'total_habitaciones'
            }

            async with AsyncSessionLocal() as session:
                try:
                    count = 0
                    for _, row in df.iterrows():
                        if pd.notna(row.get('CodHotel')):
                            # Verificar si el hotel ya existe
                            existing = await session.execute(
                                select(hoteles).where(hoteles.codigo == str(row['CodHotel']))
                            )
                            if existing.scalar_one_or_none():
                                continue  # Skip si ya existe

                            hotel = hoteles(
                                codigo=str(row['CodHotel']),
                                nombre=str(row.get('DenHotel', '')),
                                direccion=None,
                                municipio_codigo=str(row.get('NomMunHotel', '')),
                                provincia_codigo=str(row.get('ProvHotel', '')).zfill(2),
                                zona_destino=str(row.get('ZonaDest', '')),
                                categoria=str(row.get('CatHotel', '')),
                                total_habitaciones=int(row.get('NumEstancOfert', 0)) if pd.notna(row.get('NumEstancOfert')) else None,
                                total_plazas=int(row.get('NumPlazasOfert', 0)) if pd.notna(row.get('NumPlazasOfert')) else None,
                                servicios={},
                                activo=True
                            )
                            session.add(hotel)
                            count += 1

                    await session.commit()
                    logger.info(f"✅ Importados {count} hoteles desde {sheet_name}")
                    total_count += count

                except Exception as e:
                    await session.rollback()
                    logger.error(f"❌ Error importando hoteles desde {sheet_name}: {e}")

        return total_count

    async def _import_distribuciones_l1_t(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar distribuciones L1 con transporte"""
        return await self._import_distribuciones_generic(excel_data, 'L1-T', 'L1', 'T')

    async def _import_distribuciones_l1_s(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar distribuciones L1 sin transporte"""
        return await self._import_distribuciones_generic(excel_data, 'L1-S', 'L1', 'S')

    async def _import_distribuciones_l2_t(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar distribuciones L2 con transporte"""
        return await self._import_distribuciones_generic(excel_data, 'L2-T', 'L2', 'T')

    async def _import_distribuciones_l2_s(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar distribuciones L2 sin transporte"""
        return await self._import_distribuciones_generic(excel_data, 'L2-S', 'L2', 'S')

    async def _import_distribuciones_l3(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar distribuciones L3"""
        return await self._import_distribuciones_generic(excel_data, 'L3-TS', 'L3', 'T')

    async def _import_distribuciones_generic(self, excel_data: Dict[str, pd.DataFrame],
                                           sheet_name: str, lote: str, tipo_turno: str) -> int:
        """Importar distribuciones de plazas de forma genérica"""
        if sheet_name not in excel_data:
            logger.warning(f"⚠️ Hoja '{sheet_name}' no encontrada")
            return 0

        df = excel_data[sheet_name]
        if df.empty:
            return 0

        async with AsyncSessionLocal() as session:
            try:
                # Limpiar distribuciones existentes para este lote y tipo
                await session.execute(
                    delete(distribuciones_plazas).where(
                        distribuciones_plazas.lote == lote,
                        distribuciones_plazas.tipo_turno == tipo_turno
                    )
                )

                count = 0

                # Procesar según estructura de cada hoja
                if sheet_name == 'L3-TS':
                    count = await self._process_l3_distributions(session, df, lote)
                else:
                    count = await self._process_l1_l2_distributions(session, df, lote, tipo_turno)

                await session.commit()
                logger.info(f"✅ Importadas {count} distribuciones desde {sheet_name}")
                return count

            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error importando distribuciones desde {sheet_name}: {str(e)}")

    async def _process_l1_l2_distributions(self, session: AsyncSession, df: pd.DataFrame,
                                         lote: str, tipo_turno: str) -> int:
        """Procesar distribuciones L1/L2 con estructura matricial"""
        count = 0

        # Las primeras filas contienen headers especiales
        if len(df) < 4:
            return 0

        # Fila 1: Zonas de destino
        # Fila 2: Días de turno
        # Fila 3: Headers adicionales
        # Fila 4+: Datos por provincia

        zonas_destino_row = df.iloc[0].fillna('')
        dias_turno_row = df.iloc[1].fillna('')

        # Procesar cada fila de datos (desde fila 3 en adelante)
        for idx in range(3, len(df)):
            row = df.iloc[idx]
            provincia_nombre = str(row.iloc[0]).strip()

            if not provincia_nombre or provincia_nombre == '':
                continue

            # Buscar código de provincia
            provincia_codigo = await self._get_provincia_codigo(session, provincia_nombre)
            if not provincia_codigo:
                continue

            # Procesar cada columna de datos
            for col_idx in range(1, len(row)):
                plazas = row.iloc[col_idx]

                if pd.isna(plazas) or plazas == '' or plazas == 0:
                    continue

                zona_destino = str(zonas_destino_row.iloc[col_idx]).strip()
                dias_turno = dias_turno_row.iloc[col_idx]

                if zona_destino and dias_turno:
                    try:
                        distribucion = distribuciones_plazas(
                            lote=lote,
                            provincia_origen=provincia_codigo,
                            zona_destino=zona_destino,
                            dias_turno=int(dias_turno),
                            tipo_turno=tipo_turno,
                            plazas_establecidas=int(plazas),
                            temporada='2025',
                            activo=True
                        )
                        session.add(distribucion)
                        count += 1
                    except (ValueError, TypeError):
                        continue

        return count

    async def _process_l3_distributions(self, session: AsyncSession, df: pd.DataFrame, lote: str) -> int:
        """Procesar distribuciones L3 con estructura específica"""
        count = 0

        # L3 tiene estructura diferente - implementar según análisis de la hoja
        # TODO: Analizar estructura específica de L3-TS

        return count

    async def _get_provincia_codigo(self, session: AsyncSession, nombre: str) -> Optional[str]:
        """Obtener código de provincia por nombre"""
        try:
            result = await session.execute(
                select(provincias.codigo).where(provincias.nombre.ilike(f"%{nombre}%"))
            )
            return result.scalar_one_or_none()
        except:
            return None

    async def _import_validation_rules(self, excel_data: Dict[str, pd.DataFrame]) -> int:
        """Importar reglas de validación desde DATAVALIDATION"""
        if 'DATAVALIDATION' not in excel_data:
            logger.warning("⚠️ Hoja 'DATAVALIDATION' no encontrada, usando reglas por defecto")
            return await self._create_default_validation_rules()

        # TODO: Procesar reglas desde la hoja DATAVALIDATION
        return await self._create_default_validation_rules()

    async def _create_default_validation_rules(self) -> int:
        """Crear reglas de validación por defecto basadas en el análisis del sistema actual"""
        reglas_default = [
            {
                'codigo': 'FIRST_TRANSPORT_BUS',
                'nombre': 'Primer transporte vuelta debe ser autocar',
                'descripcion': 'Si TipoTurno=T, entonces Trans1Vuelta debe ser A',
                'tipo': 'transporte',
                'parametros': {'campo': 'trans_1_vuelta', 'valor_requerido': 'A', 'condicion': 'tipo_turno=T'},
                'activa': True
            },
            {
                'codigo': 'LAST_TRANSPORT_BUS',
                'nombre': 'Último transporte ida debe ser autocar',
                'descripcion': 'El último transporte de ida debe ser autocar',
                'tipo': 'transporte',
                'parametros': {'validar_ultimo_transporte': True},
                'activa': True
            },
            {
                'codigo': 'DEPARTURE_TIME_MIN',
                'nombre': 'Hora mínima de salida',
                'descripcion': 'Hora de inicio no puede ser antes de las 06:00 (L1/L2) o 08:00 (L3)',
                'tipo': 'horario',
                'parametros': {'hora_minima_l1_l2': '06:00', 'hora_minima_l3': '08:00'},
                'activa': True
            },
            {
                'codigo': 'DEPARTURE_TIME_MAX',
                'nombre': 'Hora máxima de salida',
                'descripcion': 'Hora de inicio no puede ser después de las 22:00',
                'tipo': 'horario',
                'parametros': {'hora_maxima': '22:00'},
                'activa': True
            },
            {
                'codigo': 'KM_LIMIT_BUS_ONLY',
                'nombre': 'Límite kilómetros solo autocar',
                'descripcion': 'Si solo hay autocares, máximo 499km',
                'tipo': 'transporte',
                'parametros': {'km_maximo': 499, 'solo_autocares': True},
                'activa': True
            },
            {
                'codigo': 'SERVICE_REQUIRED',
                'nombre': 'Servicio obligatorio en ruta',
                'descripcion': 'Se requiere servicio según horarios de salida y llegada',
                'tipo': 'servicio',
                'parametros': {'reglas_horario': True},
                'activa': True
            },
            {
                'codigo': 'HOTEL_CAPACITY',
                'nombre': 'Capacidad de hotel',
                'descripcion': 'El hotel debe tener suficientes plazas disponibles',
                'tipo': 'hotel',
                'parametros': {'validar_capacidad': True},
                'activa': True
            },
            {
                'codigo': 'SAME_SEASON',
                'nombre': 'Temporada única',
                'descripcion': 'Todas las filas deben tener la misma temporada',
                'tipo': 'distribucion',
                'parametros': {'campo': 'temporada', 'valor_unico': True},
                'activa': True
            },
            {
                'codigo': 'USE_ALL_AIRPORTS',
                'nombre': 'Usar todos los aeropuertos',
                'descripcion': 'Todos los aeropuertos IATA deben aparecer al menos una vez',
                'tipo': 'transporte',
                'parametros': {'validar_todos_aeropuertos': True},
                'activa': True
            },
            {
                'codigo': 'DISTRIBUTION_MATCH',
                'nombre': 'Coincidencia de distribuciones',
                'descripcion': 'Las plazas usadas deben coincidir con las establecidas',
                'tipo': 'distribucion',
                'parametros': {'validar_plazas_establecidas': True},
                'activa': True
            }
        ]

        async with AsyncSessionLocal() as session:
            try:
                await session.execute(delete(reglas_validacion))

                count = 0
                for regla_data in reglas_default:
                    regla = reglas_validacion(
                        codigo=regla_data['codigo'],
                        nombre=regla_data['nombre'],
                        descripcion=regla_data['descripcion'],
                        tipo=regla_data['tipo'],
                        parametros=regla_data['parametros'],
                        activa=regla_data['activa']
                    )
                    session.add(regla)
                    count += 1

                await session.commit()
                logger.info(f"✅ Creadas {count} reglas de validación por defecto")
                return count

            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error creando reglas de validación: {str(e)}")

    async def handle_file_import(self, file_path: str, import_type: str,
                               action: str = 'ask') -> Dict[str, Any]:
        """
        Manejar importación de archivos con opciones de sobreescribir/añadir

        Args:
            file_path: Ruta del archivo a importar
            import_type: Tipo de importación ('hoteles', 'transportes', 'vuelos', 'trenes')
            action: Acción a realizar ('ask', 'overwrite', 'append')
        """
        logger.info(f"🔄 Iniciando importación de {import_type} desde {file_path}")

        try:
            # Leer archivo
            if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                df = pd.read_excel(file_path)
            elif file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                raise FileProcessingError("Formato de archivo no soportado", file_path)

            # Verificar si hay datos existentes
            existing_count = await self._count_existing_data(import_type)

            # Determinar acción si no se especifica
            if action == 'ask' and existing_count > 0:
                return {
                    'status': 'confirmation_required',
                    'message': f'Ya existen {existing_count} registros de {import_type}. ¿Qué desea hacer?',
                    'existing_count': existing_count,
                    'new_count': len(df),
                    'options': ['overwrite', 'append', 'cancel']
                }

            # Procesar importación
            if action == 'overwrite' or existing_count == 0:
                result = await self._import_with_overwrite(df, import_type)
            elif action == 'append':
                result = await self._import_with_append(df, import_type)
            else:
                return {'status': 'cancelled', 'message': 'Importación cancelada'}

            return {
                'status': 'success',
                'message': f'Importación de {import_type} completada',
                'imported_count': result['count'],
                'action_taken': action if action != 'ask' else 'overwrite',
                'details': result.get('details', {})
            }

        except Exception as e:
            logger.error(f"❌ Error en importación de {import_type}: {e}")
            return {
                'status': 'error',
                'message': f'Error importando {import_type}: {str(e)}',
                'error_details': str(e)
            }

    async def _count_existing_data(self, import_type: str) -> int:
        """Contar registros existentes según tipo de importación"""
        async with AsyncSessionLocal() as session:
            try:
                if import_type == 'hoteles':
                    result = await session.execute(select(func.count(hoteles.codigo)))
                elif import_type == 'vuelos':
                    result = await session.execute(select(func.count(vuelos.id)))
                elif import_type == 'trenes':
                    result = await session.execute(select(func.count(trenes.id)))
                else:
                    return 0

                return result.scalar() or 0
            except:
                return 0

    async def _import_with_overwrite(self, df: pd.DataFrame, import_type: str) -> Dict[str, Any]:
        """Importar datos sobreescribiendo los existentes"""
        async with AsyncSessionLocal() as session:
            try:
                # Limpiar datos existentes
                if import_type == 'hoteles':
                    await session.execute(delete(hoteles))
                    await session.execute(delete(hotel_disponibilidad))
                elif import_type == 'vuelos':
                    await session.execute(delete(vuelos))
                elif import_type == 'trenes':
                    await session.execute(delete(trenes))

                # Importar nuevos datos
                count = await self._process_import_data(session, df, import_type)

                await session.commit()
                logger.info(f"✅ Sobreescritos {count} registros de {import_type}")

                return {'count': count, 'action': 'overwrite'}

            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error sobreescribiendo {import_type}: {str(e)}")

    async def _import_with_append(self, df: pd.DataFrame, import_type: str) -> Dict[str, Any]:
        """Importar datos añadiéndolos a los existentes"""
        async with AsyncSessionLocal() as session:
            try:
                # Importar nuevos datos sin limpiar
                count = await self._process_import_data(session, df, import_type, append_mode=True)

                await session.commit()
                logger.info(f"✅ Añadidos {count} registros de {import_type}")

                return {'count': count, 'action': 'append'}

            except Exception as e:
                await session.rollback()
                raise DatabaseError(f"Error añadiendo {import_type}: {str(e)}")

    async def _process_import_data(self, session: AsyncSession, df: pd.DataFrame,
                                 import_type: str, append_mode: bool = False) -> int:
        """Procesar datos de importación según el tipo"""
        count = 0

        if import_type == 'hoteles':
            count = await self._process_hoteles_import(session, df, append_mode)
        elif import_type == 'vuelos':
            count = await self._process_vuelos_import(session, df, append_mode)
        elif import_type == 'trenes':
            count = await self._process_trenes_import(session, df, append_mode)

        return count

    async def _process_hoteles_import(self, session: AsyncSession, df: pd.DataFrame,
                                    append_mode: bool = False) -> int:
        """Procesar importación de hoteles desde archivo Anexo 11C"""
        count = 0

        # Mapear columnas esperadas del Anexo 11C
        expected_columns = [
            'CodHotel', 'DenHotel', 'NomMunHotel', 'ProvHotel',
            'ZonaDest', 'CatHotel', 'NumPlazasOfert', 'NumEstancOfert',
            'FechaDesde', 'FechaHasta'
        ]

        for _, row in df.iterrows():
            try:
                codigo_hotel = str(row.get('CodHotel', ''))
                if not codigo_hotel:
                    continue

                # En modo append, verificar si ya existe
                if append_mode:
                    existing = await session.execute(
                        select(hoteles).where(hoteles.codigo == codigo_hotel)
                    )
                    if existing.scalar_one_or_none():
                        continue  # Skip si ya existe

                # Crear registro de hotel
                hotel = hoteles(
                    codigo=codigo_hotel,
                    nombre=str(row.get('DenHotel', '')),
                    direccion=None,
                    municipio_codigo=str(row.get('NomMunHotel', '')),
                    provincia_codigo=str(row.get('ProvHotel', '')).zfill(2),
                    zona_destino=str(row.get('ZonaDest', '')),
                    categoria=str(row.get('CatHotel', '')),
                    total_habitaciones=int(row.get('NumEstancOfert', 0)) if pd.notna(row.get('NumEstancOfert')) else None,
                    total_plazas=int(row.get('NumPlazasOfert', 0)) if pd.notna(row.get('NumPlazasOfert')) else None,
                    servicios={},
                    activo=True
                )
                session.add(hotel)

                # Crear disponibilidad si hay fechas
                if pd.notna(row.get('FechaDesde')) and pd.notna(row.get('FechaHasta')):
                    disponibilidad = hotel_disponibilidad(
                        hotel_codigo=codigo_hotel,
                        fecha_inicio=pd.to_datetime(row['FechaDesde']).date(),
                        fecha_fin=pd.to_datetime(row['FechaHasta']).date(),
                        plazas_disponibles=int(row.get('NumPlazasOfert', 0)),
                        precio_por_plaza=None,
                        temporada='2025'
                    )
                    session.add(disponibilidad)

                count += 1

            except Exception as e:
                logger.warning(f"⚠️ Error procesando hotel {row.get('CodHotel', 'N/A')}: {e}")
                continue

        return count

    async def _process_vuelos_import(self, session: AsyncSession, df: pd.DataFrame,
                                   append_mode: bool = False) -> int:
        """Procesar importación de vuelos"""
        count = 0

        for _, row in df.iterrows():
            try:
                # Mapear columnas según estructura esperada
                vuelo = vuelos(
                    codigo_vuelo=str(row.get('CodigoVuelo', '')),
                    aerolinea=str(row.get('Aerolinea', '')),
                    aeropuerto_origen=str(row.get('AeropuertoOrigen', '')),
                    aeropuerto_destino=str(row.get('AeropuertoDestino', '')),
                    hora_salida=pd.to_datetime(row.get('HoraSalida')).time() if pd.notna(row.get('HoraSalida')) else None,
                    hora_llegada=pd.to_datetime(row.get('HoraLlegada')).time() if pd.notna(row.get('HoraLlegada')) else None,
                    precio=float(row.get('Precio', 0)) if pd.notna(row.get('Precio')) else None,
                    plazas_disponibles=int(row.get('PlazasDisponibles', 0)) if pd.notna(row.get('PlazasDisponibles')) else None,
                    activo=True
                )
                session.add(vuelo)
                count += 1

            except Exception as e:
                logger.warning(f"⚠️ Error procesando vuelo: {e}")
                continue

        return count

    async def _process_trenes_import(self, session: AsyncSession, df: pd.DataFrame,
                                   append_mode: bool = False) -> int:
        """Procesar importación de trenes y AVE"""
        count = 0

        for _, row in df.iterrows():
            try:
                tren = trenes(
                    numero_tren=str(row.get('NumeroTren', '')),
                    tipo=str(row.get('Tipo', 'TREN')),
                    estacion_origen=str(row.get('EstacionOrigen', '')),
                    estacion_destino=str(row.get('EstacionDestino', '')),
                    hora_salida=pd.to_datetime(row.get('HoraSalida')).time() if pd.notna(row.get('HoraSalida')) else None,
                    hora_llegada=pd.to_datetime(row.get('HoraLlegada')).time() if pd.notna(row.get('HoraLlegada')) else None,
                    precio=float(row.get('Precio', 0)) if pd.notna(row.get('Precio')) else None,
                    plazas_disponibles=int(row.get('PlazasDisponibles', 0)) if pd.notna(row.get('PlazasDisponibles')) else None,
                    activo=True
                )
                session.add(tren)
                count += 1

            except Exception as e:
                logger.warning(f"⚠️ Error procesando tren: {e}")
                continue

        return count

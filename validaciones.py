from openpyxl import load_workbook
import csv

wb = load_workbook('APPIM2025.xlsx', data_only=True)
with open('validaciones_extract.csv', 'w', newline='', encoding='utf-8') as f:
    w = csv.writer(f)
    w.writerow(['<PERSON><PERSON>','Ra<PERSON>','Tipo','Operador','Fórmula1','Fórmula2','AllowBlank',
                'ErrorTítulo','ErrorMsg','PromptTítulo','PromptMsg'])
    for sheet in wb.worksheets:
        for dv in sheet.data_validations.dataValidation:
            w.writerow([
                sheet.title,
                dv.sqref,
                dv.type or '',
                dv.operator or '',
                dv.formula1 or '',
                dv.formula2 or '',
                dv.allowBlank,
                dv.errorTitle or '',
                dv.error or '',
                dv.promptTitle or '',
                dv.prompt or '',
            ])
print("Extracted validations to validaciones_extract.csv")

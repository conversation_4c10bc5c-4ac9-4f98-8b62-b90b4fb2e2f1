#!/usr/bin/env python3
"""
Script para inicializar la base de datos con datos del Google Sheets
"""

import asyncio
import sys
import os
from pathlib import Path

# Añadir el directorio raíz al path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import init_db
from app.services.data_import_service import DataImportService
from loguru import logger


async def main():
    """Función principal para inicializar la base de datos"""
    
    logger.info("🚀 Iniciando inicialización de base de datos...")
    
    try:
        # 1. Crear tablas
        logger.info("📋 Creando estructura de base de datos...")
        await init_db()
        logger.info("✅ Estructura de base de datos creada")
        
        # 2. Importar datos iniciales
        logger.info("📊 Importando datos iniciales desde Google Sheets...")
        import_service = DataImportService()
        results = await import_service.import_initial_data()
        
        # 3. Mostrar resumen
        logger.info("📈 Resumen de importación:")
        total_imported = 0
        for data_type, count in results.items():
            logger.info(f"  - {data_type}: {count} registros")
            total_imported += count
        
        logger.info(f"✅ Inicialización completada. Total: {total_imported} registros importados")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error en inicialización: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

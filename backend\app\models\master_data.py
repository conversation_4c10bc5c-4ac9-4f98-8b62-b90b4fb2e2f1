"""
Modelos de datos maestros
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, Text, ForeignKey, Date, DECIMAL, Time
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, CodedModel, NamedModel


class Provincia(CodedModel):
    """Modelo de provincia"""
    
    __tablename__ = "provincias"
    
    nombre = Column(String(255), nullable=False, index=True)
    comunidad_autonoma = Column(String(255))
    
    # Relaciones
    municipios = relationship("Municipio", back_populates="provincia")
    hoteles = relationship("Hotel", back_populates="provincia")
    viajes_origen = relationship("Viaje", foreign_keys="Viaje.provincia_origen", back_populates="provincia_origen_rel")
    viajes_hotel = relationship("Viaje", foreign_keys="Viaje.provincia_hotel", back_populates="provincia_hotel_rel")


class Municipio(CodedModel):
    """Modelo de municipio"""
    
    __tablename__ = "municipios"
    
    nombre = Column(String(255), nullable=False, index=True)
    provincia_codigo = Column(String(10), ForeignKey("provincias.codigo"))
    poblacion = Column(Integer)
    
    # Relaciones
    provincia = relationship("Provincia", back_populates="municipios")
    hoteles = relationship("Hotel", back_populates="municipio")


class Aeropuerto(CodedModel):
    """Modelo de aeropuerto"""
    
    __tablename__ = "aeropuertos"
    
    codigo_iata = Column(String(3), primary_key=True, index=True)
    nombre = Column(String(255), nullable=False)
    ciudad = Column(String(255))
    pais = Column(String(100))
    activo = Column(Boolean, default=True)
    
    # Relaciones
    vuelos_origen = relationship("Vuelo", foreign_keys="Vuelo.aeropuerto_origen", back_populates="aeropuerto_origen_rel")
    vuelos_destino = relationship("Vuelo", foreign_keys="Vuelo.aeropuerto_destino", back_populates="aeropuerto_destino_rel")


class ZonaDestino(CodedModel):
    """Modelo de zona de destino"""
    
    __tablename__ = "zonas_destino"
    
    nombre = Column(String(255), nullable=False)
    descripcion = Column(Text)
    activa = Column(Boolean, default=True)
    
    # Relaciones
    hoteles = relationship("Hotel", back_populates="zona_destino_rel")
    viajes = relationship("Viaje", back_populates="zona_destino_rel")
    distribuciones = relationship("DistribucionPlaza", back_populates="zona_destino_rel")


class TipoTransporte(CodedModel):
    """Modelo de tipo de transporte"""
    
    __tablename__ = "tipos_transporte"
    
    nombre = Column(String(50), nullable=False)
    descripcion = Column(Text)
    activo = Column(Boolean, default=True)


class TipoServicio(CodedModel):
    """Modelo de tipo de servicio en ruta"""
    
    __tablename__ = "tipos_servicio"
    
    nombre = Column(String(100), nullable=False)
    descripcion = Column(Text)
    activo = Column(Boolean, default=True)


class DistribucionPlaza(BaseModel):
    """Modelo de distribución de plazas por lote"""
    
    __tablename__ = "distribuciones_plazas"
    
    lote = Column(String(10), nullable=False, index=True)  # L1, L2, L3
    provincia_origen = Column(String(10), ForeignKey("provincias.codigo"))
    zona_destino = Column(String(10), ForeignKey("zonas_destino.codigo"))
    dias_turno = Column(Integer, nullable=False)
    tipo_turno = Column(String(1), nullable=False)  # T, S
    plazas_establecidas = Column(Integer, nullable=False)
    temporada = Column(String(10))
    activo = Column(Boolean, default=True)
    
    # Relaciones
    provincia_origen_rel = relationship("Provincia", foreign_keys=[provincia_origen])
    zona_destino_rel = relationship("ZonaDestino", foreign_keys=[zona_destino])

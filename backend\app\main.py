"""
Aplicación principal FastAPI para el Sistema IMSERSO
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from app.api.v1 import (
    auth,
    dashboard,
    exportacion,
    hoteles,
    pliegos,
    transportes,
    validaciones,
    viajes,
)
from app.core.config import settings
from app.core.database import engine
from app.core.exceptions import IMSERSOException


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Gestión del ciclo de vida de la aplicación"""
    # Startup
    logger.info("🚀 Iniciando Sistema IMSERSO...")
    logger.info(f"Entorno: {settings.ENVIRONMENT}")
    logger.info(f"Base de datos: {settings.DATABASE_URL.split('@')[1] if '@' in settings.DATABASE_URL else 'Local'}")
    
    # Verificar conexión a la base de datos
    try:
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        logger.info("✅ Conexión a base de datos establecida")
    except Exception as e:
        logger.error(f"❌ Error conectando a base de datos: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("🛑 Cerrando Sistema IMSERSO...")
    await engine.dispose()


# Crear aplicación FastAPI
app = FastAPI(
    title="Sistema IMSERSO API",
    description="""
    API para la gestión y validación de viajes del IMSERSO.
    
    ## Funcionalidades principales:
    
    * **Pliegos**: Procesamiento de PDFs con IA para extraer requisitos
    * **Hoteles**: Gestión de alojamientos y disponibilidad
    * **Transportes**: Gestión de vuelos, trenes y autocares
    * **Viajes**: Generación automática y validación de viajes
    * **Exportación**: Descarga de resultados en múltiples formatos
    * **Validaciones**: Motor de validación basado en reglas del IMSERSO
    """,
    version="1.0.0",
    contact={
        "name": "Equipo de Desarrollo",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
    },
    lifespan=lifespan,
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
)

# Middleware de CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware de hosts confiables (solo en producción)
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS,
    )


# Manejador de excepciones personalizado
@app.exception_handler(IMSERSOException)
async def imserso_exception_handler(request: Request, exc: IMSERSOException) -> JSONResponse:
    """Manejador para excepciones personalizadas del sistema"""
    logger.error(f"Error IMSERSO: {exc.message} - Detalles: {exc.details}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.message,
            "details": exc.details,
            "error_code": exc.error_code,
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Manejador para excepciones generales"""
    logger.error(f"Error no controlado: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Error interno del servidor",
            "details": str(exc) if settings.DEBUG else "Contacte con el administrador",
        },
    )


# Rutas de salud y estado
@app.get("/health", tags=["Sistema"])
async def health_check():
    """Verificación de salud del sistema"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "timestamp": "2025-01-07T00:00:00Z",
    }


@app.get("/", tags=["Sistema"])
async def root():
    """Endpoint raíz"""
    return {
        "message": "Sistema IMSERSO API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
    }


# Incluir routers de la API
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Autenticación"])
app.include_router(dashboard.router, prefix="/api/v1/dashboard", tags=["Dashboard"])
app.include_router(pliegos.router, prefix="/api/v1/pliegos", tags=["Pliegos"])
app.include_router(hoteles.router, prefix="/api/v1/hoteles", tags=["Hoteles"])
app.include_router(transportes.router, prefix="/api/v1/transportes", tags=["Transportes"])
app.include_router(viajes.router, prefix="/api/v1/viajes", tags=["Viajes"])
app.include_router(validaciones.router, prefix="/api/v1/validaciones", tags=["Validaciones"])
app.include_router(exportacion.router, prefix="/api/v1/exportacion", tags=["Exportación"])


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
    )

function pushSeasonInRow(rowValues, seasonsFound){
  seasonsFound.push(rowValues[headers.Temporada]);
}
  
function validateSameSeason(htmlObj, seasonsFound){
  seasonsFound = Array.from(new Set(seasonsFound)).filter((el)=>{return el != "";}); // Eliminar valores duplicados y blancos
  if (seasonsFound.length > 1){
    htmlObj.append(`<p> Se han encontrado diferentes temporadas en la misma hoja: ${seasonsFound}</p>`);
  }
}


/* Función para validar los aeropuertos
AERO1IDA, AERO2IDA, AERO3IDA y AERO4IDA. Entre todos estos campos y filas, Deben figurar al menos una vez, todos los aeropuertos de la tabla. (Los aeropuertos vienen dados en: IATA) 
*/
function pushAirportsInRow(rowValues, airportsOutgoing){
    airportsOutgoing.push(rowValues[headers.Aero1Ida]);
    airportsOutgoing.push(rowValues[headers.Aero2Ida]);
    airportsOutgoing.push(rowValues[headers.Aero3Ida]);
    airportsOutgoing.push(rowValues[headers.Aero4Ida]);
  }
  
  function validateUseAllAirports(htmlObj, airportsOutgoing){
    Logger.log("Limpiando valores de la tabla de aeropuertos");
    airportsOutgoing = Array.from(new Set(airportsOutgoing)).filter((el)=>{return el != "";});// Eliminar los valores duplicados y blancos
    var iata_sh = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(settings.iataSheetName);
    var iata_codes = [];
    iata_sh.getRange(settings.iataMapRange).getValues().forEach((value, index) =>{
      if(value[0] != ""){
        iata_codes.push(value[0]);
      }
    });
    Logger.log(iata_codes);
    var remain_code = iata_codes.filter(x => !airportsOutgoing.includes(x));
    if (remain_code.length > 0){
        htmlObj.append(`<p> Faltan por listar los aeropuertos con código: <ul>`);
        remain_code.forEach((value, index) => {
          htmlObj.append(`<li>${value}</li>`);
        });
        htmlObj.append(`</ul></p>`);
    }
  }


  function validateBookedHotelRooms(errorManager, hotelMapper, bookedHotels){
    for (let hotelId in bookedHotels){
      var requestedTotalRooms = bookedHotels[hotelId];
      var hotelInfo = hotelMapper[hotelId];
      if (hotelInfo == undefined){
        errorManager.addGlobalError(hotelId, 'Este hotel no parece que exista en el anexo8');
      }else if(requestedTotalRooms != hotelInfo["NumPlazasOfert"]){
        errorManager.addGlobalError(hotelId, `Plazas diferentes: (usadas: ${requestedTotalRooms} / Establecidas en anexo8: ${hotelInfo["NumPlazasOfert"]})`)
      }
    }
  }
  
  function pushBookedHotelRoomsInTemp(rowValues, bookedHotels){
    let hotel1 = rowValues[headers.CodHotel1Destino];
    let hotel2 = rowValues[headers.CodHotel2Destino];
    let guests = parseInt(rowValues[headers.NumTotalPlazas]);
    if (hotel1 != ""){
      let accumulatedHotel1 = bookedHotels[hotel1]
      if (accumulatedHotel1 == undefined){
        accumulatedHotel1 = 0;
      }
      bookedHotels[hotel1] = guests + accumulatedHotel1;
    }
    if (hotel2 != ""){
      let accumulatedHotel2 = bookedHotels[hotel2]
      if (accumulatedHotel2 == undefined){
        accumulatedHotel2 = 0;
      }
      bookedHotels[hotel2] = guests + accumulatedHotel2;
    }
  }

  /*function pushBookedHotelRoomsInTemp(rowValues, bookedHotels){
    let hotel1 = rowValues[headers.CodHotel1Destino];
    let hotel2 = rowValues[headers.CodHotel2Destino];
    let guests = parseInt(rowValues[headers.NumTotalPlazas]);
    let nights = parseInt(rowValues[headers.DiasTurno]) - 1;
    if (hotel1 != ""){
      let accumulatedHotel1 = bookedHotels[hotel1]
      if (accumulatedHotel1 == undefined){
        accumulatedHotel1 = 0;
      }
      bookedHotels[hotel1] = guests * nights + accumulatedHotel1;
    }
    if (hotel2 != ""){
      let accumulatedHotel2 = bookedHotels[hotel2]
      if (accumulatedHotel2 == undefined){
        accumulatedHotel2 = 0;
      }
      bookedHotels[hotel2] = guests * nights + accumulatedHotel2;
    }
  }*/
  
  function pushDistributionsInRow(rowValues, usedDistributionWithTransport, usedDistributionWithoutTransport){
    let transportType = rowValues[headers.TipoTurno]
    let uniqueId = `${rowValues[headers.CodProvOrig]}-${rowValues[headers.ZonaDest]}-${rowValues[headers.DiasTurno]}`
    if (transportType == 'T'){
      let curValue = usedDistributionWithTransport[uniqueId];
      let totalInRow = parseInt(rowValues[headers.NumTotalPlazas])
      if(curValue == undefined){
        curValue = totalInRow;
      }else{
        curValue = curValue + totalInRow;
      }
      usedDistributionWithTransport[uniqueId] = curValue;
    }else{
      let curValue = usedDistributionWithoutTransport[uniqueId];
      let totalInRow = parseInt(rowValues[headers.NumTotalPlazas])
      if(curValue == undefined){
        curValue = totalInRow;
      }else{
        curValue = curValue + totalInRow;
      }
      usedDistributionWithoutTransport[uniqueId] = curValue;
    }
  }
  
  function validateUsedDistributions(errorManager, usedDistributionWithTransport, usedDistributionWithoutTransport, relatedSheets){
    let distributionsWithTransport = getDistributionDays(relatedSheets.withTransport);
    for (var key in usedDistributionWithTransport){
      let tipoTurno = "con transporte";
      let exists = distributionsWithTransport[key];
      let used = usedDistributionWithTransport[key] || 0;
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (!exists){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas: 0)`
        )
      }
    }
    for (var key in distributionsWithTransport){
      let tipoturno = "con transporte";
      let used = usedDistributionWithTransport[key] || 0;
      let needed = distributionsWithTransport[key];
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (used != needed){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoturno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas:${needed}).`
        )
      }
    }
    let distributionsWithoutTransport = getDistributionDays(relatedSheets.withoutTransport);
    for (var key in usedDistributionWithoutTransport){
      let tipoTurno = "sin transporte";
      let exists = distributionsWithoutTransport[key];
      let used = usedDistributionWithoutTransport[key] || 0;
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (!exists){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas: 0)`
        )
      }
    }
    for (var key in distributionsWithoutTransport){
      let tipoTurno = "sin transporte";
      let used = usedDistributionWithoutTransport[key] || 0;
      let needed = distributionsWithoutTransport[key];
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (used != needed){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas:${needed})`
        )
      }
    }
  }



function validateUsedL3Distributions(errorManager, usedDistributionWithTransport, usedDistributionWithoutTransport, relatedSheets){
    let distributions = getL3DistributionDays();
    let distributionsWithTransport = distributions.T;
    for (var key in usedDistributionWithTransport){
      let tipoTurno = "con transporte";
      let exists = distributionsWithTransport[key];
      let used = usedDistributionWithTransport[key] || 0;
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (!exists){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas: 0)`
        )
      }
    }
    for (var key in distributionsWithTransport){
      let tipoTurno = "con transporte";
      let used = usedDistributionWithTransport[key] || 0;
      let needed = distributionsWithTransport[key];
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (used != needed){
        errorManager.addGlobalError(
          key,
        `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas:${needed})`
        )
      }
    }
    let distributionsWithoutTransport = distributions.S;
    for (var key in usedDistributionWithoutTransport){
      let tipoTurno = "sin transporte";
      let exists = distributionsWithoutTransport[key];
      let used = usedDistributionWithoutTransport[key] || 0;
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (!exists){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas: 0)`
        )
      }
    }
    for (var key in distributionsWithoutTransport){
      let tipoTurno = "sin transporte";
      let used = usedDistributionWithoutTransport[key] || 0;
      let needed = distributionsWithoutTransport[key];
      let unsplit = key.split('-');
      let origin = unsplit[0];
      let destination = unsplit[1];
      let diasTurno = unsplit[2];
      if (used != needed){
        errorManager.addGlobalError(
          key,
          `Plazas para el destino ${destination} con origen ${origin} para el tipo turno ${tipoTurno} y ${diasTurno} días turno son diferentes a las establecidas (Usadas: ${used} / Establecidas:${needed})`
        )
      }
    }
  }
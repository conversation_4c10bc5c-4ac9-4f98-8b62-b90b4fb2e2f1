function getDistributionDays(sheetName){
  var provincesMap = getProvincesMap();
  var distributionMatrix = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName).getRange(settings.distributionDaysMapRange[sheetName]).getValues();
  // Rows 0 and 1 are the headers
  var originHeader;
  var timeHeader;
  var distributionDaysDict = {};
  distributionMatrix.forEach((row, rowIndex) =>{
    if (rowIndex == 0){
      originHeader = row;
    }else if(rowIndex == 1){
      timeHeader = row;
    }else{
      var provinceCode = provincesMap.nameToCode(row[0]);
      if (provinceCode){
        for (colIndex = 1; colIndex < row.length; colIndex++){
          let uniqueId = `${provinceCode}-${originHeader[colIndex]}-${timeHeader[colIndex]}`;
          distributionDaysDict[uniqueId] = row[colIndex];
        }
      }
    }
  });
  return distributionDaysDict;
}


function testGetL3DistributionDays(){
  Logger.log(getL3DistributionDays())
}


function getL3DistributionDays(){
  var distributionMatrix = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('L3-TS').getRange('A1:R57').getValues();
  console.log(distributionMatrix);
  var interiorProvCodeIndex = 1;
  var exteriorProvCodeIndex = 10;
  var daysHeader;
  var originHeader;
  var distributionHeader;
  var distributionDaysDict = {};
  var distributionDaysSDict = {};

  distributionMatrix.forEach((row, rowIndex) =>{
    if (rowIndex == 0){
      console.log('Do nothing here, not interested'); // row header has nothing
    }else if(rowIndex == 1){
      daysHeader = row;
    }else if(rowIndex == 2){
      distributionHeader = row;
    }else if(rowIndex == 3){
      originHeader = row;
    }else{
      // Interior Mapping
      var provCode = row[interiorProvCodeIndex];
      for (colIndex = interiorProvCodeIndex+1; colIndex < exteriorProvCodeIndex-3; colIndex++){
        if (row[colIndex] != ''){
          let uniqueId = `${provCode}-${originHeader[colIndex]}-${daysHeader[colIndex]}`;
          if (distributionHeader[colIndex] == 'S'){
            distributionDaysSDict[uniqueId] = row[colIndex];
          }else{
            distributionDaysDict[uniqueId] = row[colIndex];
          }
        }
      }
      // Exterior Mapping
      var provCode = row[exteriorProvCodeIndex];
      for (colIndex = exteriorProvCodeIndex+1; colIndex < 14; colIndex++){
        if (row[colIndex] != ''){
          let uniqueId = `${provCode}-${originHeader[colIndex]}-${daysHeader[colIndex]}`;
          if (distributionHeader[colIndex] == 'S'){
            distributionDaysSDict[uniqueId] = row[colIndex];
          }else{
            distributionDaysDict[uniqueId] = row[colIndex];
          }
        }
      }
    }
  });
  return {
    "T": distributionDaysDict,
    "S": distributionDaysSDict
  }
}
"""
Configuración de la base de datos
"""

from typing import AsyncGenerator

from sqlalchemy import MetaData
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from app.core.config import settings

# URL de base de datos directa (forzar asyncpg)
DATABASE_URL = "postgresql+asyncpg://imserso_user:imserso_pass@postgres:5432/imserso"

# Crear engine asíncrono
engine = create_async_engine(
    DATABASE_URL,
    echo=settings.DEBUG if hasattr(settings, 'DEBUG') else False,
    future=True,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Crear sessionmaker asíncrono
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)

# Metadata con convención de nombres para constraints
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s",
}

metadata = MetaData(naming_convention=convention)

# Base para modelos
Base = declarative_base(metadata=metadata)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency para obtener sesión de base de datos
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Inicializar base de datos (crear tablas)
    """
    async with engine.begin() as conn:
        # Importar todos los modelos para que sean registrados
        from app.models import (  # noqa
            hotel,
            pliego,
            transport,
            user,
            validation,
            viaje,
        )
        
        # Crear todas las tablas
        await conn.run_sync(Base.metadata.create_all)


async def drop_db() -> None:
    """
    Eliminar todas las tablas (solo para testing)
    """
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

var anexodcFieldSpec = [
          ["NumReg", 6],
          ["NumEstanCatHotel3", 8],
          ["NumEstanCatHotel4", 8],
          ["NumEstanCatHotel5", 8],
          ["NumMuniDestino", 6],
          ["NumEstancHabDobUsoInd", 8],
          ["NumEstancHotelAcc", 8],
          ["NumEstancHabAcc", 8],
          ["NumEstancHabWifi", 8],
          ["OfreceAppMovil", 1],
          ["NumPasajAvionAve", 8],
          ["NumAerSalida", 4],
          ["NumTotalAutob", 6],
          ["NumAutobAcce", 6],
          ["NumAutobWc", 6],
          ["HeterIllesBaleares", 1],
          ["HeterCanarias", 1],
          ["OfertaPorcentPermanencia", 1],
      ]

var anexo5FieldSpec = [
          ["NumReg", 6],
          ["CodAgencia", 20],
          ["DenEmpresa", 40],
          ["DenAgencia", 40],
          ["CodPostal", 5],
          ["CodProvIne", 2],
          ["NomProvIne", 20],
          ["CodMunIne", 3],
          ["NomMunIne", 40],
          ["DomAgencia", 80],
          ["PrimerTfno", 20],
          ["SegundoTfno", 20]
      ]

var anexo8FieldSpec = [
          ["NumReg", 6 ],
          ["Temporada", 4],
          ["ZonaDest", 3],
          ["ComAutHotel", 2],
          ["ProvHotel", 2],
          ["NomMunHotel", 30],
          ["EmpresaHotel", 30],
          ["CodFiscEmpresa", 12],
          ["DenHotel", 30],
          ["CodHotel", 10],
          ["CatHotel", 1],
          ["NumPlazasHotel", 6],
          ["NumPlazasHabDob", 6 ],
          ["NumPlazasHabInd", 6],
          ["NumPlazasHabAcc", 6],
          ["NumPlazasOfert", 6],
          ["NumEstancHabDob", 6],
          ["NumEstancHabDobUsoInd", 6],
          ["NumEstancHabAcc", 6],
          ["NumEstancOfert", 6],
          ["FechaDesde", 8],
          ["FechaHasta", 8],
          ["DiasOfert", 3],
          ["NumEstancHabWifi", 6],
          ["CertAcce", 1],
          ["CodMunIne", 3]
      ]


var anexo7FieldSpec = [
        ["NumReg", 6 ],
        ["Temporada", 4],
        ["TipoTurno", 1],
        ["ZonaDest", 3],
        ["CodProvHotel", 3],
        ["DiasTurno", 2],
        ["CodProvOrig", 3],
        ["NumTotalPlazas", 6],
        ["HoraInicioIda", 4],
        ["Trans1Ida", 1],
        ["Aero1Ida", 3],
        ["HoraInicio2Ida", 4],
        ["MunSalida2Ida", 30],
        ["Trans2Ida", 1],
        ["Aero2Ida", 3],
        ["HoraInicio3Ida", 4],
        ["MunSalida3Ida", 30],
        ["Trans3Ida", 1],
        ["Aero3Ida", 3],
        ["HoraInicio4Ida", 4],
        ["MunSalida4Ida", 30],
        ["Trans4Ida", 1],
        ["Aero4Ida", 3],
        ["Serv1RutaIda", 1],
        ["Mun1RutaIda", 30],
        ["Estab1RutaIda", 30],
        ["Serv2RutaIda", 1],
        ["Mun2RutaIda", 30],
        ["Estab2RutaIda", 30],
        ["MunDestIda", 30],
        ["HoraFinIda", 4],
        ["TotalKmIda", 4],
        ["TransComb", 1],
        ["SegundoMunBalCan", 30],
        ["HoraInicioVuelta", 4],
        ["Trans1Vuelta", 1],
        ["Aero1Vuelta", 3],
        ["HoraInicio2Vuelta", 4],
        ["MunSalida2Vuelta", 30],
        ["Trans2Vuelta", 1],
        ["Aero2Vuelta", 3],
        ["HoraInicio3Vuelta", 4],
        ["MunSalida3Vuelta", 30],
        ["Trans3Vuelta", 1],
        ["Aero3Vuelta", 3],
        ["HoraInicio4Vuelta", 4],
        ["MunSalida4Vuelta", 30],
        ["Trans4Vuelta", 1],
        ["Aero4Vuelta", 3],
        ["Serv1RutaVuelta", 1],
        ["Mun1RutaVuelta", 30],
        ["Estab1RutaVuelta", 30],
        ["Serv2RutaVuelta", 1],
        ["Mun2RutaVuelta", 30],
        ["Estab2RutaVuelta", 30],
        ["CapProvDestVuelta", 30],
        ["HoraFinVuelta", 4],
        ["TotalKmVuelta", 4],
        ["CodHotel1Destino", 10],
        ["CodHotel2Destino", 10],
        ["NumTotalAutob", 6],
        ["NumAutobAcce", 6],
        ["NumAutobWc", 6],
        ["ViajeCombinado", 1],
        ["NumHorasAnimac", 6],
        ["NumHorasSanit", 6],
        ["FechaInicioViaje", 8],
    ]

function isNumeric(str) {
  if (typeof str != "string") {
    if (typeof str == "number"){
      str = Math.ceil(str)
    }
    str = str.toString();
  } // we only process strings!  
  return !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
         !isNaN(parseFloat(str)) // ...and ensure strings of whitespace fail
}

function isEmpty(value) {
  return value === '' || value === null || value === undefined;
}

function fullFill(value, maxLenght, force_type){
  //Logger.log(value);
  if (force_type =='S'){
      value = value.toString();
      value = value.replace(/\n/g, '');
      value = sanitize(value);
      return fillWithSpaces(value, maxLenght);
  }
  if(isNumeric(value)){
    value = value.toString();
    value = value.replace(/\n/g, '');
    value = sanitize(value);
    return fillWithNumbers(value, maxLenght)
  }
  value = sanitize(value);
  return fillWithSpaces(value.replace(/\n/g, ''), maxLenght)
}

function fillWithSpaces(value, maxLenght){
    let maxIndex = maxLenght;
    let result = value.slice(0, maxIndex).padEnd(maxLenght, " ");
    return result
  }

function fillWithNumbers (value, maxLenght) {
  return value.length < maxLenght ? fillWithNumbers("0" + value, maxLenght) : value;
}

function exportSheetToValid(){
  
  var ui = SpreadsheetApp.getUi(); // Same variations.
  var is_outcome = false;
  var mandatory_field = null;
  if (sheet.getSheetName().includes("Anexo")){
    if(sheet.getSheetName().includes('5')){
      mandatory_field = "CodAgencia";
      var fieldsSize = anexo5FieldSpec;
    }else if(sheet.getSheetName().includes('8')){
      mandatory_field = "ZonaDest";
      var fieldsSize = anexo8FieldSpec;
    }else if(sheet.getSheetName().includes('7') || sheet.getSheetName().includes('OUTCOME')){
      is_outcome = true;
      mandatory_field = "TipoTurno";
      var fieldsSize = anexo7FieldSpec;
    }else if(sheet.getSheetName().includes('DC')){
      is_outcome = false;
      mandatory_field = "NumEstanCatHotel3";
      var fieldsSize = anexodcFieldSpec;
    }
  }else if(isValidOutcomeSheet(sheet.getSheetName())){
    is_outcome = true;
    mandatory_field = "TipoTurno";
    var fieldsSize = anexo7FieldSpec;
  }else{
    ui.alert('Parece que no podemos determinar tu tipo de hoja');
    return
  }
  var sheetData = rangeData.getValues();
  var rows = [];
  var autMapper = getAutMapper();
  //resultData.push(sheetData[0]);
  for (rowIndex = 1; rowIndex <= sheetData.length; rowIndex++){
    var rowValues = sheetData[rowIndex];
    Logger.log(`Exportando fila ${rowIndex}`);
    //Terminar la ejecución cuando no exista un NumReg
    if (!rowValues || isEmpty(rowValues[headers[mandatory_field]])){
      Logger.log('Parando ejecución');
      break;
    }
    let row = []
    fieldsSize.forEach(function (specs, index){
        let header_name = specs[0];
        let size = specs[1];
        let force_type = undefined;
        let value = sheetData[rowIndex][headers[header_name]]
        if (header_name == "FechaDesde" || header_name == "FechaHasta" || header_name == "FechaInicioViaje"){
          value = value.replaceAll('/', '');
        }
        if (header_name == "ComAutHotel"){
          if (value == ""){
            value = autMapper[sheetData[rowIndex][headers["ProvHotel"]]]
            //Logger.log("New value is "+ value);
          }
        }
        if (header_name == "NumHorasAnimac"){
          if (value == ""){
            value = "0";
          }
        }
        if (header_name == "NumHorasSanit"){
          if (value == ""){
            value = "0";
          }
        }
        if (header_name == "PrimerTfno"){
          force_type = "S"
        }
        if (header_name == "SegundoTfno"){
          force_type = "S"
        }
        //Logger.log(header_name)
        let result = fullFill(value, size, force_type)
        if (result.length != size){
          Logger.log('Result is not that size')
        }
        //Logger.log(result);
        row.push(result)
    });
    rows.push(row.join(''));
  }
  saveAsTXT(rows);
}

function sanitize(texto){
  var codificaciones = {
    "Ã±": "ñ",
    "Ã©": "é",
    "Ã³": "ó",
    "Ã¡": "á",
    "Ã­": "í",
    "Ãº": "ú",
    "Ã": "Á",
    "Ã‰": "É",
    "Ã“": "Ó",
    "Ãš": "Ú",
    "Â": "",
    "â€˜": "'",
    "â€™": "'",
    "Âº": "º",
    "Ã": "Ñ",
    "Ã‡": "Ç",
    "Ã": "í",
    "Â¡": "¡",
    "Â¿": "¿",
    "Dâ€™": "D'",
    "S/N-": "S/N"
  };

  // Reemplazar caracteres mal codificados
  for (var codificacion in codificaciones) {
    var valor = codificaciones[codificacion];
    texto = texto.replace(new RegExp(codificacion, "g"), valor);
  }
  return texto;
}

function getExportFileName(sheet){
  var name = sheet.getSheetName().toLowerCase();
  var lote = name.split('-')[0][1];
  var anexo = name.split('-')[1];
  if (anexo == "outcome"){
    anexo = "anexo7";
  }
  return `${anexo}lote${lote}.txt`
}

function saveAsTXT(rows) {
  var ss = SpreadsheetApp.getActiveSpreadsheet(); 
  var sheet = ss.getActiveSheet();
  // create a folder from the name of the spreadsheet
  var folder = getOrCreateFolder("APP2025/APPExporter");
  // append ".csv" extension to the sheet name
  fileName = getExportFileName(sheet);
  // convert all available sheet data to csv format
  var csvFile = convertRangeToTxtFile_(fileName, rows);
  // create a file in the Docs List with the given name and the csv data
  files = folder.getFilesByName(fileName);
  file = null;
  while (files.hasNext()){
    file = files.next();
  }
  if(!file){
    file = folder.createFile(fileName, csvFile);
  }else{
    file.setContent(csvFile);
  }
  //File downlaod
  file.setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW);
  var downloadURL = file.getDownloadUrl().slice(0, -8);
  showurl(downloadURL);
}

function showurl(downloadURL) {
  var htmlOutput = HtmlService.createHtmlOutput('').setTitle('Descargar fichero');
  htmlOutput.append(`Abre el siguiente enlace en el navegador para descargar el fichero:<br> <a href="${downloadURL}" target="_blank">${downloadURL}</a>`)
  ui.showSidebar(htmlOutput);
}

function convertRangeToTxtFile_(csvFileName, rows) {
  // get available data range in the spreadsheet
  try {
    var csvFile = undefined;

    // loop through the data in the range and build a string with the csv data
    var csv = "";
    for (var row = 0; row < rows.length; row++) {
      // join each row's columns
      // add a carriage return to end of each row, except for the last one
      if (row < rows.length-1) {
        csv += rows[row] + "\r\n";
      }
      else {
        csv += rows[row];
      }
    }
    csvFile = csv;
    return csvFile;
  }
  catch(err) {
    Logger.log(err);
    Browser.msgBox(err);
  }
}


function getOrCreateFolder(path) {
  var folders = path.split("/");
  var parent = DriveApp.getRootFolder();
  
  for (var i = 0; i < folders.length; i++) {
    var folderName = folders[i];
    var foundFolders = parent.getFoldersByName(folderName);
    
    if (foundFolders.hasNext()) {
      parent = foundFolders.next();
    } else {
      parent = parent.createFolder(folderName);
    }
  }
  return parent;
}

function testgetOrCreateFolder(){
  getOrCreateFolder("APP2025/APPExporter");
}

{"info": {"name": "🚀 Sistema IMSERSO API", "description": "Colección completa para probar el Sistema IMSERSO\n\n## 🎯 Funcionalidades principales:\n- ✈️ Generación automática de viajes\n- ✅ Validaciones con 47+ reglas\n- 📤 Exportación de archivos OUTCOME\n- 🏨 Gestión de hoteles y transportes\n\n## 🔧 Setup:\n1. Configura environment: base_url = http://localhost:8000\n2. Ejecuta Login para obtener token\n3. ¡Prueba todas las funcionalidades!", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}], "item": [{"name": "🏥 Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/health", "host": ["{{base_url}}"], "path": ["api", "v1", "health"]}, "description": "Verifica estado del sistema y base de datos"}, "response": []}, {"name": "🔐 Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('token_type', response.token_type);", "    console.log('Token guardado:', response.access_token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "admin123", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login y obtención de token JWT"}, "response": []}, {"name": "🚀 <PERSON><PERSON> (PRINCIPAL)", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON>jes generados', function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"lote\": \"L1\",\n    \"temporada\": \"2025\",\n    \"optimize_routes\": true,\n    \"include_transfers\": true,\n    \"max_distance_km\": 800\n}"}, "url": {"raw": "{{base_url}}/api/v1/viajes/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "via<PERSON>s", "generate"]}, "description": "🚀 FUNCIONALIDAD PRINCIPAL: Genera viajes automáticamente con optimización de rutas"}, "response": []}, {"name": "📋 <PERSON>ar <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/viajes/?lote=L1&limit=20", "host": ["{{base_url}}"], "path": ["api", "v1", "via<PERSON>s", ""], "query": [{"key": "lote", "value": "L1"}, {"key": "limit", "value": "20"}]}, "description": "Lista viajes generados con filtros"}, "response": []}, {"name": "✅ Validar <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"lote\": \"L1\",\n    \"temporada\": \"2025\",\n    \"validate_all_rules\": true,\n    \"strict_mode\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/validaciones/validate-batch", "host": ["{{base_url}}"], "path": ["api", "v1", "validaciones", "validate-batch"]}, "description": "🔍 Valida lote completo con 47+ reglas IMSERSO"}, "response": []}, {"name": "📤 Exportar Resultados", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"lote\": \"L1\",\n    \"temporada\": \"2025\",\n    \"formato\": \"xlsx\",\n    \"include_validations\": true,\n    \"include_costs\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/exportacion/export", "host": ["{{base_url}}"], "path": ["api", "v1", "exportacion", "export"]}, "description": "📤 Exporta archivos OUTCOME listos para entregar"}, "response": []}, {"name": "🏨 Importar Hoteles", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Archivo Excel con hoteles"}, {"key": "overwrite", "value": "false", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/hoteles/import", "host": ["{{base_url}}"], "path": ["api", "v1", "hoteles", "import"]}, "description": "Importa hoteles desde archivo Excel"}, "response": []}, {"name": "🚌 Importar Transportes", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Archivo Excel con transportes"}, {"key": "tipo", "value": "vuelo", "type": "text", "description": "vuelo, tren, autocar"}]}, "url": {"raw": "{{base_url}}/api/v1/transportes/import", "host": ["{{base_url}}"], "path": ["api", "v1", "transportes", "import"]}, "description": "Importa transportes (vuelos, trenes, autocares)"}, "response": []}, {"name": "📊 Dashboard Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/dashboard/stats", "host": ["{{base_url}}"], "path": ["api", "v1", "dashboard", "stats"]}, "description": "Estadísticas generales del sistema"}, "response": []}, {"name": "📋 Procesar Pliego PDF", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{token_type}} {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Archivo PDF del pliego"}, {"key": "lote", "value": "L1", "type": "text"}, {"key": "temporada", "value": "2025", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/v1/pliegos/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "pliegos", "upload"]}, "description": "Sube y procesa pliego PDF con IA"}, "response": []}]}
"""
Endpoints de autenticación
"""

from datetime import timed<PERSON><PERSON>
from typing import Any

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.core.exceptions import AuthenticationError
from app.schemas.user import Token, User, UserCreate, UserInDB

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")


@router.post("/login", response_model=Token)
async def login_for_access_token(
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # TODO: Implementar autenticación real
    if form_data.username == "admin" and form_data.password == "admin123":
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        return {
            "access_token": "fake-token-for-now",
            "token_type": "bearer",
            "expires_in": access_token_expires.total_seconds(),
        }
    else:
        raise AuthenticationError("Credenciales incorrectas")


@router.post("/register", response_model=User)
async def register(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Registrar nuevo usuario
    """
    # TODO: Implementar registro real
    return {
        "id": "fake-uuid",
        "username": user_in.username,
        "email": user_in.email,
        "full_name": user_in.full_name,
        "is_active": True,
        "is_superuser": False,
    }


@router.get("/me", response_model=User)
async def read_users_me(
    current_user: User = Depends(oauth2_scheme),
) -> Any:
    """
    Obtener usuario actual
    """
    # TODO: Implementar obtención de usuario real
    return {
        "id": "fake-uuid",
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Administrador",
        "is_active": True,
        "is_superuser": True,
    }

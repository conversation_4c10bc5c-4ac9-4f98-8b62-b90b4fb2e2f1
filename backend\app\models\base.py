"""
Modelo base para todos los modelos de la aplicación
"""

import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy.sql import func


@as_declarative()
class Base:
    """Clase base para todos los modelos"""
    
    id: Any
    __name__: str
    
    # Generar nombre de tabla automáticamente
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


class BaseModel(Base):
    """Modelo base con campos comunes"""
    
    __abstract__ = True
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
    )
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>"


class NamedModel(BaseModel):
    """Modelo base con nombre"""
    
    __abstract__ = True
    
    nombre = Column(String(255), nullable=False, index=True)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id}, nombre='{self.nombre}')>"


class CodedModel(BaseModel):
    """Modelo base con código"""
    
    __abstract__ = True
    
    codigo = Column(String(20), unique=True, nullable=False, index=True)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(codigo='{self.codigo}')>"

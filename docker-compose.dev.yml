version: '3.8'

# Configuración para desarrollo con hot-reload
services:
  postgres:
    image: postgres:15-alpine
    container_name: imserso_postgres_dev
    environment:
      POSTGRES_DB: imserso_dev
      POSTGRES_USER: imserso_dev
      POSTGRES_PASSWORD: dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - imserso_dev_network

  redis:
    image: redis:7-alpine
    container_name: imserso_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    networks:
      - imserso_dev_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: imserso_backend_dev
    environment:
      - DATABASE_URL=***************************************************/imserso_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-not-for-production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=development
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
      - DEBUG=true
      - RELOAD=true
    volumes:
      - ./backend:/app
      - ./storage:/app/storage
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - imserso_dev_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: imserso_frontend_dev
    environment:
      - VITE_API_URL=http://localhost:8001
      - VITE_APP_TITLE=Sistema IMSERSO (DEV)
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - imserso_dev_network
    command: npm run dev -- --host 0.0.0.0

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  imserso_dev_network:
    driver: bridge

var ui = SpreadsheetApp.getUi();
var sheet = SpreadsheetApp.getActiveSheet();
var rangeData = sheet.getDataRange();
var lastColumn = rangeData.getLastColumn();
var lastRow = rangeData.getLastRow();


/*
Headers object return the index for a requested header.
So for example:
headers.NumReg, will return 0, because it's the first item on the header
*/
var _headers = sheet.getRange(1, 1, 1, lastColumn).getValues()[0];
var headers = new Object();
for (i = 0; i < _headers.length; i++){
  headers[_headers[i]] = i;
}

function moveToRange(row, col){
  sheet.setActiveSelection(sheet.getRange(row, col));
}


function onOpen() {
  ui.createMenu('Validaciones')
  .addItem('Validar todos los datos','validateSheet')
  .addItem('Limpiar errores', 'cleanErrors')
  .addItem('Establecer temporada', 'stablishSeason')
  .addItem('Exportar a formato válido', 'exportSheetToValid')
  .addItem('Limpiar hoja outcome', 'cleanOutcomeSheet')
  .addItem('Establecer fórmulas de autocompletado', 'stablishFormulas')
  .addItem('Establecer número de registro', 'stablishNumReg')
  .addToUi();
}

function fillArray(value, len) {
  var arr = [];
  for (var i = 0; i < len; i++) {
    arr.push(value);
  }
  return arr;
}

function stablishSeason(){
  var ui = SpreadsheetApp.getUi(); // Same variations.
  if (!isValidOutcomeSheet(sheet.getSheetName())){
    ui.alert("Esta no parece una hoja de OUTCOME válida");
    return;
  };
  var result = ui.prompt(
      'Selecciona la temporada que quieres establecer!',
      'Temporada:',
      ui.ButtonSet.OK_CANCEL);

  // Process the user's response.
  var button = result.getSelectedButton();
  var seasonValues = fillArray([result.getResponseText()], lastRow-2);
  if (button == ui.Button.OK) {
    // User clicked "OK".
    sheet.getRange(2,headers.Temporada+1, lastRow-2).setValues(seasonValues);
  }
}

function cleanErrors(){
  sheet.getRange(2, 1, lastRow, lastColumn)
    .setFontStyle(settings.normalFontStyle)
    .setFontColor(settings.normalFontColor)
    .setFontWeight(settings.normalFontWeight);
}

function validateSheet(){
  if (!isValidOutcomeSheet(sheet.getSheetName())){
    ui.alert("Esta no parece una hoja de OUTCOME válida");
    return;
  };
  let relatedSheets = getRelatedSheets(sheet.getSheetName());
  console.log(relatedSheets);
  var htmlOutput = HtmlService.createHtmlOutput('').setTitle('Validation Check');
  let errorManager = new ErrorManager();
  cleanErrors();
  var sheetData = rangeData.getValues();
  var airportsOutgoing = []; // Listado de aeropuertos que hemos usado para la ida
  var usedDistributionWithTransport = {}; // Listado de las distribuciones usadas
  var usedDistributionWithoutTransport = {}; // Listado de las distribuciones usadas
  var seasonsFound = [];
  // To be uncommented once everything is working fine
  var hotelMapper = getHotelMap(relatedSheets.hotelSheetName);
  var bookedHotels = {};
  // Avoid hitting headers by starting with index 1
  //ROW BASED VALIDATIONS
  var numberOfRowsValidated = 0;
  for (rowIndex = 1; rowIndex <= sheetData.length; rowIndex++){
    var rowValues = sheetData[rowIndex];
    var rowIndexInSheet = rowIndex+1; // Sheet index starts at 1
    Logger.log(`Validando fila ${rowIndex}`);
    //Terminar la ejecución cuando no exista un NumReg
    if (!rowValues || !rowValues[headers.TipoTurno]){
      continue;
    }
    //Valida el primer transporte de vuelta sea un autocar
    validateFirstTransportShouldBeBus(errorManager, rowValues, rowIndexInSheet);
    //Valida las horas de inicio
    validateDepartureTime(errorManager, rowValues, rowIndexInSheet);
    //Valida todas las HoraInicioIda
    validateNextDepartureTime(errorManager, rowValues, rowIndexInSheet, relatedSheets.sheetType);
    //Valida que el ultimo transporte sea un autocar
    validateLastTransportShouldBeBus(errorManager, rowValues, rowIndexInSheet);
    // Valida los tiempos de vuelta
    validateReturnDepartureTime(errorManager, rowValues, rowIndexInSheet, relatedSheets.sheetType);
    // Validar el tiempo de llegada
    validateArrivalTime(errorManager, rowValues, rowIndexInSheet);
    // Validar los Kms de ida y vuelta
    validateTripKms(errorManager, rowValues, rowIndexInSheet);
    // Validar servicios
    validateServiceOnTrip(errorManager, rowValues, rowIndexInSheet);
    // Validar habitaciones disponibles
    // To be uncommented once everything is working fine
    validateAvailableHotelRooms(errorManager, rowValues, rowIndexInSheet, hotelMapper)
    //Añade en airportsOutgoing los aeropuertos que hemos usado en Ida
    pushAirportsInRow(rowValues, airportsOutgoing);
    // Añade los hoteles agregados por tiempo
    pushBookedHotelRoomsInTemp(rowValues, bookedHotels);  // NOT VALIDATES
    //Añade las temporadas encontradas en las filas
    pushSeasonInRow(rowValues, seasonsFound);
    //Añade las distribuciones usadas en la hoja
    pushDistributionsInRow(rowValues, usedDistributionWithTransport, usedDistributionWithoutTransport);
    numberOfRowsValidated++;
  }
  // END ROW BASED VALIDATOS
  //Acumulative validations
  if(relatedSheets.sheetType == "L3"){
    validateUsedL3Distributions(errorManager, usedDistributionWithTransport, usedDistributionWithoutTransport, relatedSheets)
  }else{
    validateUsedDistributions(errorManager, usedDistributionWithTransport, usedDistributionWithoutTransport, relatedSheets)
  }
  // Validar los codigos de hotel
  // validateBookedHotelRooms(errorManager, hotelMapper, bookedHotels);  // NOT VALIDATED
  htmlOutput.append(errorManager.html())
  validateUseAllAirports(htmlOutput, airportsOutgoing);
  validateSameSeason(htmlOutput, seasonsFound);
  errorManager.failedCells().forEach(cell => {
    let rowIndex = cell[0];
    let colIndex = cell[1];
    markColumnAsError(rowIndex, colIndex);
  });
  if (htmlOutput.getContent() == ""){
    htmlOutput.append("<h1>¡Enhorabuena!</h1>");
    htmlOutput.append('<p>No se han encontrado errores en la hoja de outcome</p>');
    htmlOutput.append('<img src="https://media.giphy.com/media/g9582DNuQppxC/giphy-downsized.gif">');
    htmlOutput.append('<button onclick="google.script.run.exportSheetToValid()">Exportar a formato válido</button>')
  }
  htmlOutput.append(`<p>${numberOfRowsValidated} filas validadas.</p>`)
  ui.showSidebar(htmlOutput);
}

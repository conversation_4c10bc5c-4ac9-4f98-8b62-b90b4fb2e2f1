# Sistema de Generación Inteligente de Viajes IMSERSO

## 🎯 Objetivo

El sistema genera automáticamente las hojas **L1-OUTCOME**, **L2-OUTCOME** y **L3-OUTCOME** optimizando rutas, precios y cumpliendo todas las validaciones del IMSERSO.

## 🚀 Características Principales

### ✅ Optimización Inteligente
- **Rutas Directas**: Prioriza conexiones directas cuando son eficientes
- **Conexiones Optimizadas**: Encuentra la mejor combinación de transportes via hubs principales
- **Minimización de Distancia**: Evita rutas innecesarias (ej: Madrid→Bilbao→Sevilla)
- **Optimización de Precios**: Balancea coste y eficiencia

### ✅ Cumplimiento Total de Validaciones
- **Transportes**: Primer/último autocar, límites de kilómetros
- **Horarios**: Mínimos/máximos según lote, secuencias correctas
- **Servicios**: Obligatorios según horarios de salida/llegada
- **Hoteles**: Capacidad suficiente, disponibilidad real
- **Distribuciones**: Coincidencia exacta con plazas establecidas

### ✅ Algoritmos Avanzados
- **Grafos de Rutas**: Modelado de la red de transporte española
- **Algoritmo de Dijkstra**: Búsqueda de rutas óptimas
- **Heurísticas de Coste**: Combinación de distancia, tiempo y precio
- **Validación en Tiempo Real**: Verificación durante la generación

## 🗺️ Arquitectura del Sistema

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Distribuciones  │───▶│  TripGenerator   │───▶│ Viajes Generados│
│ de Plazas       │    │                  │    │ (L1/L2/L3)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ RouteOptimizer   │
                    │ - Rutas directas │
                    │ - Conexiones     │
                    │ - Hubs           │
                    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ ValidationEngine │
                    │ - 47+ reglas     │
                    │ - Tiempo real    │
                    └──────────────────┘
```

## 📊 Proceso de Generación

### 1. Lectura de Requisitos
```python
# Lee distribuciones de plazas desde hojas L1-T, L1-S, L2-T, L2-S, L3-TS
requirements = await get_trip_requirements(lote="L1", temporada="2025")
```

### 2. Búsqueda de Hoteles
```python
# Encuentra hotel óptimo en zona de destino con capacidad suficiente
hotel = await find_optimal_hotel(requirement)
```

### 3. Optimización de Rutas
```python
# Optimiza ruta ida: Madrid → Sevilla
transportes_ida = await optimize_route(
    origen="28",      # Madrid
    destino="41",     # Sevilla
    lote="L1",
    direccion="ida"
)
```

### 4. Validación Automática
```python
# Valida contra todas las reglas IMSERSO
is_valid = await validate_generated_trip(optimized_trip)
```

## 🛣️ Algoritmo de Optimización de Rutas

### Criterios de Optimización (en orden de prioridad):

1. **Distancia Mínima**: Evita rutas innecesarias
2. **Coste Mínimo**: Optimiza precio por persona
3. **Menos Transbordos**: Prioriza rutas directas
4. **Cumplimiento de Horarios**: Respeta límites temporales

### Tipos de Rutas Consideradas:

#### 🚌 Autocar Directo
- **Condición**: Distancia ≤ 499km (regla IMSERSO)
- **Ventaja**: Sin transbordos, flexible en horarios
- **Coste**: ~€0.15 por km por persona

#### ✈️ Vuelo Directo
- **Condición**: Aeropuertos disponibles en origen/destino
- **Ventaja**: Rápido para distancias largas
- **Coste**: Variable según disponibilidad

#### 🚄 Tren/AVE Directo
- **Condición**: Conexión ferroviaria disponible
- **Ventaja**: Cómodo, puntual
- **Coste**: Variable según tipo (Tren/AVE)

#### 🔄 Rutas con Conexión
- **Hubs Principales**: Madrid, Barcelona, Sevilla, Valencia, Bilbao
- **Validación**: Mínimo 60 minutos entre conexiones
- **Optimización**: Mejor combinación de tramos

## 📋 Validaciones Implementadas

### Validaciones de Transporte
```python
# Primer transporte vuelta = autocar
if viaje.tipo_turno == 'T' and viaje.trans_1_vuelta != 'A':
    error("Primer transporte de vuelta ha de ser siempre un A (autocar)")

# Último transporte ida = autocar  
if ultimo_transporte_ida != 'A':
    error("El último transporte ha de ser siempre un A (autocar)")

# Límite kilómetros solo autocar
if solo_autocares and total_km > 499:
    error("El total de kms excede el límite de 499kms")
```

### Validaciones de Horarios
```python
# Hora mínima según lote
hora_minima = time(8, 0) if lote == 'L3' else time(6, 0)

if hora_inicio < hora_minima:
    error(f"Hora demasiado pronto (mínimo {hora_minima})")

# Secuencia correcta
if hora_siguiente <= hora_anterior:
    error("Secuencia de horarios inválida")
```

### Validaciones de Servicios
```python
# Servicio obligatorio según horarios
requiere_servicio = (
    (inicio < time(14, 0) and fin > time(15, 1)) or
    (inicio > time(15, 1) and fin > time(22, 1))
)

if requiere_servicio and not servicio_ruta:
    error("Debes establecer un servicio en la ruta")
```

## 🔧 Uso del Sistema

### Generar Viajes para un Lote
```bash
# Generar L1 completo
curl -X POST "http://localhost:8000/api/v1/viajes/generate" \
  -H "Content-Type: application/json" \
  -d '{"lote": "L1", "temporada": "2025"}'
```

### Validar Viajes Generados
```bash
# Validar lote completo
curl -X POST "http://localhost:8000/api/v1/validaciones/validate-batch" \
  -H "Content-Type: application/json" \
  -d '{"lote": "L1", "temporada": "2025"}'
```

### Exportar Resultados
```bash
# Exportar a Excel (formato OUTCOME)
curl -X POST "http://localhost:8000/api/v1/exportacion/export" \
  -H "Content-Type: application/json" \
  -d '{"formato": "xlsx", "lote": "L1"}'
```

## 📈 Métricas de Optimización

### Ejemplo de Resultados:
```json
{
  "lote": "L1",
  "viajes_generados": 1500,
  "viajes_validos": 1485,
  "viajes_con_errores": 15,
  "optimizacion": {
    "rutas_directas": 1200,
    "rutas_con_conexion": 285,
    "km_promedio_ida": 320,
    "km_promedio_vuelta": 320,
    "precio_promedio": 85.50,
    "tiempo_generacion": "45 segundos"
  },
  "validaciones": {
    "transportes_validos": 1485,
    "horarios_validos": 1490,
    "servicios_validos": 1495,
    "hoteles_validos": 1500,
    "distribuciones_validas": 1500
  }
}
```

## 🎯 Ventajas vs Sistema Manual

| Aspecto | Sistema Manual | Sistema Automático |
|---------|----------------|-------------------|
| **Tiempo** | Días/semanas | Minutos |
| **Errores** | Frecuentes | Mínimos (validación automática) |
| **Optimización** | Subjetiva | Algoritmos matemáticos |
| **Consistencia** | Variable | 100% consistente |
| **Validaciones** | Manual, propensa a errores | Automática, exhaustiva |
| **Escalabilidad** | Limitada | Ilimitada |

## 🔮 Funcionalidades Futuras

- **Machine Learning**: Aprendizaje de patrones de optimización
- **Predicción de Demanda**: Ajuste dinámico de capacidades
- **Integración Tiempo Real**: APIs de aerolíneas y RENFE
- **Optimización Multi-Objetivo**: Sostenibilidad, confort, etc.
- **Simulación de Escenarios**: "¿Qué pasaría si...?"

---

**El sistema transforma la generación manual de viajes en un proceso automático, optimizado y libre de errores, cumpliendo al 100% con las exigentes validaciones del IMSERSO.**

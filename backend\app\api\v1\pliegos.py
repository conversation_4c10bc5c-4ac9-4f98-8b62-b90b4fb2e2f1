"""
Endpoints para gestión de pliegos
"""

from typing import Any, List

from fastapi import APIRouter, Depends, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db

router = APIRouter()


@router.post("/upload")
async def upload_pliego(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Subir y procesar pliego PDF
    """
    # TODO: Implementar procesamiento real con IA
    return {
        "id": "fake-uuid",
        "filename": file.filename,
        "status": "procesando",
        "message": "Pliego subido correctamente, procesando con IA...",
    }


@router.get("/")
async def list_pliegos(
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Listar todos los pliegos
    """
    # TODO: Implementar listado real
    return {
        "pliegos": [
            {
                "id": "1",
                "nombre": "PPT.pdf",
                "año": 2025,
                "tipo": "PPT",
                "estado": "procesado",
                "fecha_procesamiento": "2025-01-07T10:00:00Z",
            }
        ]
    }


@router.get("/{pliego_id}")
async def get_pliego(
    pliego_id: str,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Obtener detalles de un pliego
    """
    # TODO: Implementar obtención real
    return {
        "id": pliego_id,
        "nombre": "PPT.pdf",
        "año": 2025,
        "tipo": "PPT",
        "estado": "procesado",
        "contenido_extraido": {
            "requisitos": ["Requisito 1", "Requisito 2"],
            "plazas_minimas": {"L1": 1000, "L2": 800, "L3": 600},
        },
    }


@router.delete("/{pliego_id}")
async def delete_pliego(
    pliego_id: str,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Eliminar un pliego
    """
    # TODO: Implementar eliminación real
    return {"message": f"Pliego {pliego_id} eliminado correctamente"}

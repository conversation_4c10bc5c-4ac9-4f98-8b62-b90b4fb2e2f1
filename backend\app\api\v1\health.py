"""Endpoint de health check"""
from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db

router = APIRouter()


@router.get("/health")
async def health_check(db: AsyncSession = Depends(get_db)) -> Any:
    """
    Health check del sistema
    
    Verifica:
    - Estado del servidor
    - Conexión a base de datos
    - Timestamp actual
    """
    
    try:
        # Probar conexión a base de datos
        result = await db.execute(text("SELECT 1"))
        db_status = "connected" if result else "error"
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "service": "Sistema IMSERSO Backend",
            "database": {
                "status": db_status,
                "type": "PostgreSQL"
            },
            "environment": "development"
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "service": "Sistema IMSERSO Backend",
            "database": {
                "status": "error",
                "error": str(e)
            },
            "environment": "development"
        }


@router.get("/")
async def root() -> Any:
    """Endpoint raíz de la API"""
    return {
        "message": "Sistema IMSERSO API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health",
        "status": "running"
    }

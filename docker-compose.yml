version: '3.8'

services:
  # Base de datos PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: imserso_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-imserso}
      POSTGRES_USER: ${POSTGRES_USER:-imserso_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-imserso_pass}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - imserso_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-imserso_user} -d ${POSTGRES_DB:-imserso}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis para caché y sesiones
  redis:
    image: redis:7-alpine
    container_name: imserso_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_pass}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - imserso_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: imserso_backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-imserso_user}:${POSTGRES_PASSWORD:-imserso_pass}@postgres:5432/${POSTGRES_DB:-imserso}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_pass}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000}
    volumes:
      - ./storage:/app/storage
      - ./backend/app:/app/app
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - imserso_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # # Frontend React
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: imserso_frontend
  #   environment:
  #     - VITE_API_URL=${VITE_API_URL:-http://localhost:8000}
  #     - VITE_APP_TITLE=${VITE_APP_TITLE:-Sistema IMSERSO}
  #   volumes:
  #     - ./frontend/src:/app/src
  #     - ./frontend/public:/app/public
  #   ports:
  #     - "3000:3000"
  #   depends_on:
  #     - backend
  #   networks:
  #     - imserso_network

  # Nginx Proxy Reverso
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: imserso_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./storage/exports:/var/www/exports
    depends_on:
      - backend
      # - frontend
    networks:
      - imserso_network

  # MinIO para almacenamiento de archivos (opcional)
  minio:
    image: minio/minio:latest
    container_name: imserso_minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - imserso_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  imserso_network:
    driver: bridge

services:
  # Backend FastAPI
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: imserso_backend
    environment:
      - DATABASE_URL=****************************************************/imserso
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./backend:/app
      - backend_storage:/app/storage
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - imserso_network

  # Base de datos PostgreSQL
  postgres:
    image: postgres:15
    container_name: imserso_postgres
    environment:
      POSTGRES_DB: imserso
      POSTGRES_USER: imserso_user
      POSTGRES_PASSWORD: imserso_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - imserso_network

  # Redis para cache
  redis:
    image: redis:7-alpine
    container_name: imserso_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - imserso_network

volumes:
  postgres_data:
  redis_data:
  backend_storage:

networks:
  imserso_network:
    driver: bridge
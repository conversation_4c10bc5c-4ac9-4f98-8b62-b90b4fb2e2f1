"""
Esquemas para exportación de viajes basados en las hojas OUTCOME
"""

from typing import Optional, List
from pydantic import BaseModel
from datetime import time


class ViajeExportBase(BaseModel):
    """Esquema base para exportación de viajes (común a L1, L2, L3)"""
    
    # Campos básicos (comunes a todas las hojas OUTCOME)
    NumReg: int
    Temporada: str
    TipoTurno: str  # T o S
    ZonaDest: str
    ProvHotel: str
    DiasTurno: int
    ProvOrig: str
    NumTotalPlazas: int
    
    # Hoteles
    CodHotel1Destino: str
    CodHotel2Destino: Optional[str] = None
    
    # Transportes IDA
    Trans1Ida: Optional[str] = None
    Trans2Ida: Optional[str] = None
    Trans3Ida: Optional[str] = None
    Trans4Ida: Optional[str] = None
    
    # Transportes VUELTA
    Trans1Vuelta: Optional[str] = None
    Trans2Vuelta: Optional[str] = None
    Trans3Vuelta: Optional[str] = None
    Trans4Vuelta: Optional[str] = None
    
    # Aeropuertos
    Aero1Ida: Optional[str] = None
    Aero2Ida: Optional[str] = None
    Aero3Ida: Optional[str] = None
    Aero4Ida: Optional[str] = None
    
    # Horarios IDA
    HoraInicioIda: Optional[str] = None  # Formato HHMM
    HoraInicio2Ida: Optional[str] = None
    HoraInicio3Ida: Optional[str] = None
    HoraInicio4Ida: Optional[str] = None
    HoraFinIda: Optional[str] = None
    
    # Horarios VUELTA
    HoraInicioVuelta: Optional[str] = None
    HoraInicio2Vuelta: Optional[str] = None
    HoraInicio3Vuelta: Optional[str] = None
    HoraInicio4Vuelta: Optional[str] = None
    HoraFinVuelta: Optional[str] = None
    
    # Kilómetros
    TotalKmIda: Optional[int] = None
    TotalKmVuelta: Optional[int] = None
    
    # Servicios en ruta
    Serv1RutaIda: Optional[str] = None
    Serv1RutaVuelta: Optional[str] = None
    
    # Campos adicionales
    ViajeCombinado: str = 'N'


class ViajeExportL1(ViajeExportBase):
    """Esquema específico para exportación L1-OUTCOME"""
    
    # Campos específicos de L1 (si los hay)
    pass


class ViajeExportL2(ViajeExportBase):
    """Esquema específico para exportación L2-OUTCOME"""
    
    # Campos específicos de L2 (si los hay)
    pass


class ViajeExportL3(ViajeExportBase):
    """Esquema específico para exportación L3-OUTCOME"""
    
    # Campos específicos de L3 (basado en análisis de la hoja)
    # Agregar campos adicionales específicos de L3 si existen
    pass


class ExportRequest(BaseModel):
    """Esquema para solicitud de exportación"""
    
    formato: str  # 'csv', 'xlsx', 'txt'
    lote: Optional[str] = None  # L1, L2, L3 o None para todos
    filtros: Optional[dict] = None
    incluir_headers: bool = True
    separador_csv: str = ','
    encoding: str = 'utf-8'


class ExportResponse(BaseModel):
    """Esquema para respuesta de exportación"""
    
    export_id: str
    filename: str
    formato: str
    total_registros: int
    estado: str  # 'procesando', 'completado', 'error'
    download_url: Optional[str] = None
    expires_at: Optional[str] = None
    created_at: str


class ExportStatus(BaseModel):
    """Esquema para estado de exportación"""
    
    export_id: str
    estado: str
    progreso: Optional[int] = None  # Porcentaje 0-100
    mensaje: Optional[str] = None
    error_details: Optional[str] = None


# Mapeo de columnas según el análisis de las hojas OUTCOME
OUTCOME_COLUMNS_MAPPING = {
    'L1': [
        'NumReg', 'Temporada', 'TipoTurno', 'ZonaDest', 'ProvHotel', 'DiasTurno', 
        'ProvOrig', 'NumTotalPlazas', 'CodHotel1Destino', 'CodHotel2Destino',
        'Trans1Ida', 'Trans2Ida', 'Trans3Ida', 'Trans4Ida',
        'Trans1Vuelta', 'Trans2Vuelta', 'Trans3Vuelta', 'Trans4Vuelta',
        'Aero1Ida', 'Aero2Ida', 'Aero3Ida', 'Aero4Ida',
        'HoraInicioIda', 'HoraInicio2Ida', 'HoraInicio3Ida', 'HoraInicio4Ida', 'HoraFinIda',
        'HoraInicioVuelta', 'HoraInicio2Vuelta', 'HoraInicio3Vuelta', 'HoraInicio4Vuelta', 'HoraFinVuelta',
        'TotalKmIda', 'TotalKmVuelta',
        'Serv1RutaIda', 'Serv1RutaVuelta',
        'ViajeCombinado'
    ],
    'L2': [
        'NumReg', 'Temporada', 'TipoTurno', 'ZonaDest', 'ProvHotel', 'DiasTurno',
        'ProvOrig', 'NumTotalPlazas', 'CodHotel1Destino', 'CodHotel2Destino',
        'Trans1Ida', 'Trans2Ida', 'Trans3Ida', 'Trans4Ida',
        'Trans1Vuelta', 'Trans2Vuelta', 'Trans3Vuelta', 'Trans4Vuelta',
        'Aero1Ida', 'Aero2Ida', 'Aero3Ida', 'Aero4Ida',
        'HoraInicioIda', 'HoraInicio2Ida', 'HoraInicio3Ida', 'HoraInicio4Ida', 'HoraFinIda',
        'HoraInicioVuelta', 'HoraInicio2Vuelta', 'HoraInicio3Vuelta', 'HoraInicio4Vuelta', 'HoraFinVuelta',
        'TotalKmIda', 'TotalKmVuelta',
        'Serv1RutaIda', 'Serv1RutaVuelta',
        'ViajeCombinado'
    ],
    'L3': [
        'NumReg', 'Temporada', 'TipoTurno', 'ZonaDest', 'ProvHotel', 'DiasTurno',
        'ProvOrig', 'NumTotalPlazas', 'CodHotel1Destino', 'CodHotel2Destino',
        'Trans1Ida', 'Trans2Ida', 'Trans3Ida', 'Trans4Ida',
        'Trans1Vuelta', 'Trans2Vuelta', 'Trans3Vuelta', 'Trans4Vuelta',
        'Aero1Ida', 'Aero2Ida', 'Aero3Ida', 'Aero4Ida',
        'HoraInicioIda', 'HoraInicio2Ida', 'HoraInicio3Ida', 'HoraInicio4Ida', 'HoraFinIda',
        'HoraInicioVuelta', 'HoraInicio2Vuelta', 'HoraInicio3Vuelta', 'HoraInicio4Vuelta', 'HoraFinVuelta',
        'TotalKmIda', 'TotalKmVuelta',
        'Serv1RutaIda', 'Serv1RutaVuelta',
        'ViajeCombinado'
    ]
}

# Formatos de hora para exportación
def format_time_for_export(time_obj: Optional[time]) -> Optional[str]:
    """Convertir objeto time a formato HHMM para exportación"""
    if time_obj is None:
        return None
    return f"{time_obj.hour:02d}{time_obj.minute:02d}"


def format_viaje_for_export(viaje, lote: str) -> dict:
    """Formatear un viaje para exportación según el lote"""
    
    data = {
        'NumReg': viaje.num_reg,
        'Temporada': viaje.temporada,
        'TipoTurno': viaje.tipo_turno,
        'ZonaDest': viaje.zona_destino,
        'ProvHotel': viaje.provincia_hotel,
        'DiasTurno': viaje.dias_turno,
        'ProvOrig': viaje.provincia_origen,
        'NumTotalPlazas': viaje.num_total_plazas,
        'CodHotel1Destino': viaje.hotel_destino_1,
        'CodHotel2Destino': viaje.hotel_destino_2 or '',
        
        # Transportes
        'Trans1Ida': viaje.trans_1_ida or '',
        'Trans2Ida': viaje.trans_2_ida or '',
        'Trans3Ida': viaje.trans_3_ida or '',
        'Trans4Ida': viaje.trans_4_ida or '',
        'Trans1Vuelta': viaje.trans_1_vuelta or '',
        'Trans2Vuelta': viaje.trans_2_vuelta or '',
        'Trans3Vuelta': viaje.trans_3_vuelta or '',
        'Trans4Vuelta': viaje.trans_4_vuelta or '',
        
        # Aeropuertos
        'Aero1Ida': viaje.aero_1_ida or '',
        'Aero2Ida': viaje.aero_2_ida or '',
        'Aero3Ida': viaje.aero_3_ida or '',
        'Aero4Ida': viaje.aero_4_ida or '',
        
        # Horarios
        'HoraInicioIda': format_time_for_export(viaje.hora_inicio_ida),
        'HoraInicio2Ida': format_time_for_export(viaje.hora_inicio_2_ida),
        'HoraInicio3Ida': format_time_for_export(viaje.hora_inicio_3_ida),
        'HoraInicio4Ida': format_time_for_export(viaje.hora_inicio_4_ida),
        'HoraFinIda': format_time_for_export(viaje.hora_fin_ida),
        'HoraInicioVuelta': format_time_for_export(viaje.hora_inicio_vuelta),
        'HoraInicio2Vuelta': format_time_for_export(viaje.hora_inicio_2_vuelta),
        'HoraInicio3Vuelta': format_time_for_export(viaje.hora_inicio_3_vuelta),
        'HoraInicio4Vuelta': format_time_for_export(viaje.hora_inicio_4_vuelta),
        'HoraFinVuelta': format_time_for_export(viaje.hora_fin_vuelta),
        
        # Kilómetros
        'TotalKmIda': viaje.total_km_ida or 0,
        'TotalKmVuelta': viaje.total_km_vuelta or 0,
        
        # Servicios
        'Serv1RutaIda': viaje.serv_1_ruta_ida or '',
        'Serv1RutaVuelta': viaje.serv_1_ruta_vuelta or '',
        
        # Otros
        'ViajeCombinado': viaje.viaje_combinado or 'N'
    }
    
    # Filtrar solo las columnas del lote específico
    columns = OUTCOME_COLUMNS_MAPPING.get(lote, OUTCOME_COLUMNS_MAPPING['L1'])
    return {col: data.get(col, '') for col in columns}

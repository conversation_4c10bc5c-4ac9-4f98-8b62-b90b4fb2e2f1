"""
Modelo de pliegos
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB

from app.models.base import BaseModel


class Pliego(BaseModel):
    """Modelo de pliego del IMSERSO"""
    
    __tablename__ = "pliegos"
    
    nombre = Column(String(255), nullable=False)
    año = Column(Integer, nullable=False)
    tipo = Column(String(50))  # 'PPT', 'Anexo', etc.
    archivo_original = Column(String(500))
    contenido_extraido = Column(JSONB)
    estado = Column(String(20), default='pendiente')  # pendiente, procesado, error
    fecha_procesamiento = Column(String(50))  # Timestamp como string por simplicidad

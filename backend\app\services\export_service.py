"""
Servicio para exportación de viajes en múltiples formatos
"""

import os
import csv
import pandas as pd
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.core.database import AsyncSessionLocal
from app.core.config import settings
from app.core.exceptions import ExportError
from app.models.viaje import Viaje
from app.schemas.export import ExportRequest, format_viaje_for_export, OUTCOME_COLUMNS_MAPPING


class ExportService:
    """Servicio para exportar viajes en diferentes formatos"""
    
    def __init__(self):
        self.export_dir = settings.EXPORT_DIR
        os.makedirs(self.export_dir, exist_ok=True)
    
    async def process_export(self, export_id: str, request: ExportRequest, 
                           filename: str, total_registros: int) -> None:
        """
        Procesar exportación en background
        """
        try:
            logger.info(f"🔄 Iniciando exportación {export_id}: {filename}")
            
            # Obtener datos de viajes
            viajes_data = await self._get_viajes_data(request)
            
            # Generar archivo según formato
            file_path = os.path.join(self.export_dir, f"{export_id}_{filename}")
            
            if request.formato == 'csv':
                await self._export_to_csv(viajes_data, file_path, request)
            elif request.formato == 'xlsx':
                await self._export_to_excel(viajes_data, file_path, request)
            elif request.formato == 'txt':
                await self._export_to_txt(viajes_data, file_path, request)
            
            logger.info(f"✅ Exportación {export_id} completada: {file_path}")
            
        except Exception as e:
            logger.error(f"❌ Error en exportación {export_id}: {e}")
            raise ExportError(request.formato, str(e))
    
    async def _get_viajes_data(self, request: ExportRequest) -> List[Dict[str, Any]]:
        """Obtener datos de viajes según filtros"""
        
        async with AsyncSessionLocal() as session:
            query = select(Viaje)
            
            # Filtrar por lote
            if request.lote:
                query = query.where(Viaje.lote == request.lote)
            
            # Aplicar filtros adicionales
            if request.filtros:
                if 'temporada' in request.filtros:
                    query = query.where(Viaje.temporada == request.filtros['temporada'])
                if 'estado' in request.filtros:
                    query = query.where(Viaje.estado == request.filtros['estado'])
                if 'provincia_origen' in request.filtros:
                    query = query.where(Viaje.provincia_origen == request.filtros['provincia_origen'])
            
            # Ordenar por NumReg
            query = query.order_by(Viaje.num_reg)
            
            result = await session.execute(query)
            viajes = result.scalars().all()
            
            # Convertir a formato de exportación
            viajes_data = []
            for viaje in viajes:
                # Determinar lote para formateo
                lote = request.lote or viaje.lote
                viaje_formatted = format_viaje_for_export(viaje, lote)
                viajes_data.append(viaje_formatted)
            
            return viajes_data
    
    async def _export_to_csv(self, viajes_data: List[Dict[str, Any]], 
                           file_path: str, request: ExportRequest) -> None:
        """Exportar a formato CSV"""
        
        if not viajes_data:
            raise ExportError('csv', 'No hay datos para exportar')
        
        # Obtener columnas según el lote
        columns = self._get_columns_for_export(viajes_data, request.lote)
        
        with open(file_path, 'w', newline='', encoding=request.encoding) as csvfile:
            writer = csv.DictWriter(
                csvfile, 
                fieldnames=columns,
                delimiter=request.separador_csv,
                quoting=csv.QUOTE_MINIMAL
            )
            
            if request.incluir_headers:
                writer.writeheader()
            
            for viaje in viajes_data:
                # Filtrar solo las columnas necesarias
                row = {col: viaje.get(col, '') for col in columns}
                writer.writerow(row)
    
    async def _export_to_excel(self, viajes_data: List[Dict[str, Any]], 
                             file_path: str, request: ExportRequest) -> None:
        """Exportar a formato Excel"""
        
        if not viajes_data:
            raise ExportError('xlsx', 'No hay datos para exportar')
        
        # Crear DataFrame
        df = pd.DataFrame(viajes_data)
        
        # Obtener columnas según el lote
        columns = self._get_columns_for_export(viajes_data, request.lote)
        
        # Reordenar columnas
        df = df.reindex(columns=columns, fill_value='')
        
        # Exportar a Excel
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            sheet_name = f"{request.lote}-OUTCOME" if request.lote else "VIAJES-OUTCOME"
            df.to_excel(
                writer, 
                sheet_name=sheet_name,
                index=False,
                header=request.incluir_headers
            )
            
            # Formatear hoja
            worksheet = writer.sheets[sheet_name]
            
            # Ajustar ancho de columnas
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    async def _export_to_txt(self, viajes_data: List[Dict[str, Any]], 
                           file_path: str, request: ExportRequest) -> None:
        """Exportar a formato TXT (delimitado por tabulaciones)"""
        
        if not viajes_data:
            raise ExportError('txt', 'No hay datos para exportar')
        
        # Obtener columnas según el lote
        columns = self._get_columns_for_export(viajes_data, request.lote)
        
        with open(file_path, 'w', encoding=request.encoding) as txtfile:
            # Escribir headers si se requiere
            if request.incluir_headers:
                txtfile.write('\t'.join(columns) + '\n')
            
            # Escribir datos
            for viaje in viajes_data:
                row_values = [str(viaje.get(col, '')) for col in columns]
                txtfile.write('\t'.join(row_values) + '\n')
    
    def _get_columns_for_export(self, viajes_data: List[Dict[str, Any]], 
                              lote: str = None) -> List[str]:
        """Obtener columnas para exportación según el lote"""
        
        if lote and lote in OUTCOME_COLUMNS_MAPPING:
            return OUTCOME_COLUMNS_MAPPING[lote]
        
        # Si no hay lote específico, usar todas las columnas disponibles
        if viajes_data:
            return list(viajes_data[0].keys())
        
        # Fallback a columnas de L1
        return OUTCOME_COLUMNS_MAPPING['L1']
    
    def get_export_file_path(self, export_id: str, filename: str) -> str:
        """Obtener ruta completa del archivo de exportación"""
        return os.path.join(self.export_dir, f"{export_id}_{filename}")
    
    def file_exists(self, export_id: str, filename: str) -> bool:
        """Verificar si el archivo de exportación existe"""
        file_path = self.get_export_file_path(export_id, filename)
        return os.path.exists(file_path)
    
    def delete_export_file(self, export_id: str, filename: str) -> bool:
        """Eliminar archivo de exportación"""
        try:
            file_path = self.get_export_file_path(export_id, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"🗑️ Archivo de exportación eliminado: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ Error eliminando archivo de exportación: {e}")
            return False

// Compiled using ts2gas 3.6.4 (TypeScript 4.1.5)
/**
 * Settings file
 * -------------
 * This file contains almost all the setting variables used across the application
 * Settings is just a dict containing all the values. Nothing magic at all.
 */
var settings = {
    // Mapper settings
    provinceSheetName: "Provincia",
    provicesMapRange: "A2:B",
    distributionDaysMapRange: {
        "L1-T": "A:I",
        "L1-S": "A:I",
        "L2-T": "A:E",
        "L2-S": "A:E"
    },
    hotelCode: "CODIGO HOTEL",
    hotelTotalRooms: "TOTAL HAB OFERTADAS TEMPORADA 2021-2022",
    hotelTotalBooks: "TOTAL ESTANCIAS TEMPORADA 2021-2022",
    iataSheetName: "IATA",
    iataMapRange: "A2:A",
    // Normal to error settings
    errorFontColor: "red",
    errorFontWeight: "bold",
    // Error to normal settings
    normalFontStyle: "normal",
    normalFontColor: "black",
    normalFontWeight: "normal",
    // New settings objects, to be implemented
    hotel: {
        sheetName: "Hoteles"
    },
    pack: {
        withTransport: "T",
        withoutTransport: "S"
    }
};

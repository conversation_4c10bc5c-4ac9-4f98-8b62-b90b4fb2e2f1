-- ESQUEMA DE BASE DE DATOS SISTEMA IMSERSO
-- PostgreSQL 15+

-- Extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- =====================================================
-- TABLAS DE CONFIGURACIÓN Y DATOS MAESTROS
-- =====================================================

-- Usuarios del sistema
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Provincias
CREATE TABLE provincias (
    codigo VARCHAR(10) PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    comunidad_autonoma VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Municipios
CREATE TABLE municipios (
    codigo VARCHAR(20) PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    provincia_codigo VARCHAR(10) REFERENCES provincias(codigo),
    poblacion INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Zonas de destino
CREATE TABLE zonas_destino (
    codigo VARCHAR(10) PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Aeropuertos (códigos IATA)
CREATE TABLE aeropuertos (
    codigo_iata VARCHAR(3) PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    ciudad VARCHAR(255),
    pais VARCHAR(100),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tipos de transporte
CREATE TABLE tipos_transporte (
    codigo VARCHAR(1) PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL,
    descripcion TEXT,
    activo BOOLEAN DEFAULT true
);

-- Tipos de servicio en ruta
CREATE TABLE tipos_servicio (
    codigo VARCHAR(10) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    activo BOOLEAN DEFAULT true
);

-- =====================================================
-- TABLAS DE PLIEGOS Y CONFIGURACIÓN
-- =====================================================

-- Pliegos del IMSERSO
CREATE TABLE pliegos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre VARCHAR(255) NOT NULL,
    año INTEGER NOT NULL,
    tipo VARCHAR(50), -- 'PPT', 'Anexo', etc.
    archivo_original VARCHAR(500),
    contenido_extraido JSONB,
    estado VARCHAR(20) DEFAULT 'pendiente', -- pendiente, procesado, error
    fecha_procesamiento TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Distribuciones de plazas por lote
CREATE TABLE distribuciones_plazas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lote VARCHAR(10) NOT NULL, -- L1, L2, L3
    provincia_origen VARCHAR(10) REFERENCES provincias(codigo),
    zona_destino VARCHAR(10) REFERENCES zonas_destino(codigo),
    dias_turno INTEGER NOT NULL,
    tipo_turno VARCHAR(1) NOT NULL, -- T, S
    plazas_establecidas INTEGER NOT NULL,
    temporada VARCHAR(10),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TABLAS DE HOTELES
-- =====================================================

-- Hoteles
CREATE TABLE hoteles (
    codigo VARCHAR(20) PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    direccion TEXT,
    municipio_codigo VARCHAR(20) REFERENCES municipios(codigo),
    provincia_codigo VARCHAR(10) REFERENCES provincias(codigo),
    zona_destino VARCHAR(10) REFERENCES zonas_destino(codigo),
    categoria VARCHAR(10),
    total_habitaciones INTEGER,
    total_plazas INTEGER,
    servicios JSONB,
    coordenadas POINT,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Disponibilidad y precios de hoteles por período
CREATE TABLE hotel_disponibilidad (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    hotel_codigo VARCHAR(20) REFERENCES hoteles(codigo),
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE NOT NULL,
    plazas_disponibles INTEGER NOT NULL,
    precio_por_plaza DECIMAL(10,2),
    temporada VARCHAR(10),
    observaciones TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT no_overlap_dates EXCLUDE USING gist (
        hotel_codigo WITH =,
        daterange(fecha_inicio, fecha_fin, '[]') WITH &&
    )
);

-- =====================================================
-- TABLAS DE TRANSPORTES
-- =====================================================

-- Vuelos
CREATE TABLE vuelos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    codigo_vuelo VARCHAR(20),
    aerolinea VARCHAR(100),
    aeropuerto_origen VARCHAR(3) REFERENCES aeropuertos(codigo_iata),
    aeropuerto_destino VARCHAR(3) REFERENCES aeropuertos(codigo_iata),
    hora_salida TIME,
    hora_llegada TIME,
    dias_operacion VARCHAR(20), -- L,M,X,J,V,S,D
    fecha_inicio_operacion DATE,
    fecha_fin_operacion DATE,
    precio DECIMAL(10,2),
    plazas_disponibles INTEGER,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trenes y AVE
CREATE TABLE trenes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    numero_tren VARCHAR(20),
    tipo VARCHAR(10), -- 'TREN', 'AVE'
    estacion_origen VARCHAR(100),
    estacion_destino VARCHAR(100),
    hora_salida TIME,
    hora_llegada TIME,
    dias_operacion VARCHAR(20),
    fecha_inicio_operacion DATE,
    fecha_fin_operacion DATE,
    precio DECIMAL(10,2),
    plazas_disponibles INTEGER,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Rutas de autocar (ilimitadas entre cualquier origen-destino)
CREATE TABLE rutas_autocar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    origen VARCHAR(255) NOT NULL,
    destino VARCHAR(255) NOT NULL,
    distancia_km INTEGER,
    tiempo_estimado_minutos INTEGER,
    precio_por_km DECIMAL(8,4),
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TABLAS DE VIAJES GENERADOS
-- =====================================================

-- Viajes generados (equivalente a L1-OUTCOME, L2-OUTCOME, L3-OUTCOME)
CREATE TABLE viajes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    num_reg INTEGER NOT NULL,
    lote VARCHAR(10) NOT NULL, -- L1, L2, L3
    temporada VARCHAR(10) NOT NULL,
    tipo_turno VARCHAR(1) NOT NULL, -- T, S
    zona_destino VARCHAR(10) REFERENCES zonas_destino(codigo),
    provincia_hotel VARCHAR(10) REFERENCES provincias(codigo),
    dias_turno INTEGER NOT NULL,
    provincia_origen VARCHAR(10) REFERENCES provincias(codigo),
    num_total_plazas INTEGER NOT NULL,

    -- Hoteles
    hotel_destino_1 VARCHAR(20) REFERENCES hoteles(codigo),
    hotel_destino_2 VARCHAR(20) REFERENCES hoteles(codigo),

    -- Transportes IDA
    trans_1_ida VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_2_ida VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_3_ida VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_4_ida VARCHAR(1) REFERENCES tipos_transporte(codigo),

    -- Transportes VUELTA
    trans_1_vuelta VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_2_vuelta VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_3_vuelta VARCHAR(1) REFERENCES tipos_transporte(codigo),
    trans_4_vuelta VARCHAR(1) REFERENCES tipos_transporte(codigo),

    -- Aeropuertos
    aero_1_ida VARCHAR(3) REFERENCES aeropuertos(codigo_iata),
    aero_2_ida VARCHAR(3) REFERENCES aeropuertos(codigo_iata),
    aero_3_ida VARCHAR(3) REFERENCES aeropuertos(codigo_iata),
    aero_4_ida VARCHAR(3) REFERENCES aeropuertos(codigo_iata),

    -- Horarios IDA
    hora_inicio_ida TIME,
    hora_inicio_2_ida TIME,
    hora_inicio_3_ida TIME,
    hora_inicio_4_ida TIME,
    hora_fin_ida TIME,

    -- Horarios VUELTA
    hora_inicio_vuelta TIME,
    hora_inicio_2_vuelta TIME,
    hora_inicio_3_vuelta TIME,
    hora_inicio_4_vuelta TIME,
    hora_fin_vuelta TIME,

    -- Kilómetros
    total_km_ida INTEGER,
    total_km_vuelta INTEGER,

    -- Servicios en ruta
    serv_1_ruta_ida VARCHAR(10) REFERENCES tipos_servicio(codigo),
    serv_1_ruta_vuelta VARCHAR(10) REFERENCES tipos_servicio(codigo),

    -- Campos adicionales
    viaje_combinado VARCHAR(1) DEFAULT 'N',
    observaciones TEXT,

    -- Control de estado
    estado VARCHAR(20) DEFAULT 'generado', -- generado, validado, exportado, error
    errores_validacion JSONB,

    -- Auditoría
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),

    -- Índices únicos
    UNIQUE(lote, num_reg, temporada)
);

-- =====================================================
-- TABLAS DE VALIDACIONES
-- =====================================================

-- Reglas de validación configurables
CREATE TABLE reglas_validacion (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    codigo VARCHAR(50) UNIQUE NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    tipo VARCHAR(50) NOT NULL, -- 'horario', 'transporte', 'hotel', 'distribucion'
    parametros JSONB,
    activa BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Resultados de validaciones por viaje
CREATE TABLE validaciones_viaje (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    viaje_id UUID REFERENCES viajes(id) ON DELETE CASCADE,
    regla_codigo VARCHAR(50) REFERENCES reglas_validacion(codigo),
    resultado VARCHAR(20) NOT NULL, -- 'valido', 'error', 'advertencia'
    mensaje TEXT,
    detalles JSONB,
    validado_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TABLAS DE EXPORTACIÓN Y LOGS
-- =====================================================

-- Exportaciones realizadas
CREATE TABLE exportaciones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nombre_archivo VARCHAR(255) NOT NULL,
    formato VARCHAR(10) NOT NULL, -- 'csv', 'xlsx', 'txt'
    lote VARCHAR(10),
    filtros JSONB,
    total_registros INTEGER,
    ruta_archivo VARCHAR(500),
    estado VARCHAR(20) DEFAULT 'procesando', -- procesando, completado, error
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id)
);

-- Logs del sistema
CREATE TABLE logs_sistema (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nivel VARCHAR(10) NOT NULL, -- DEBUG, INFO, WARNING, ERROR
    modulo VARCHAR(100),
    mensaje TEXT NOT NULL,
    detalles JSONB,
    usuario_id UUID REFERENCES users(id),
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices para viajes
CREATE INDEX idx_viajes_lote_temporada ON viajes(lote, temporada);
CREATE INDEX idx_viajes_provincia_origen ON viajes(provincia_origen);
CREATE INDEX idx_viajes_zona_destino ON viajes(zona_destino);
CREATE INDEX idx_viajes_estado ON viajes(estado);
CREATE INDEX idx_viajes_created_at ON viajes(created_at);

-- Índices para hoteles
CREATE INDEX idx_hoteles_zona_destino ON hoteles(zona_destino);
CREATE INDEX idx_hoteles_provincia ON hoteles(provincia_codigo);
CREATE INDEX idx_hotel_disponibilidad_fechas ON hotel_disponibilidad(fecha_inicio, fecha_fin);

-- Índices para transportes
CREATE INDEX idx_vuelos_origen_destino ON vuelos(aeropuerto_origen, aeropuerto_destino);
CREATE INDEX idx_trenes_origen_destino ON trenes(estacion_origen, estacion_destino);

-- Índices para validaciones
CREATE INDEX idx_validaciones_viaje_resultado ON validaciones_viaje(viaje_id, resultado);

-- Índices para búsquedas de texto
CREATE INDEX idx_hoteles_nombre_trgm ON hoteles USING gin(nombre gin_trgm_ops);
CREATE INDEX idx_municipios_nombre_trgm ON municipios USING gin(nombre gin_trgm_ops);

-- Inicialización de la base de datos IMSERSO
-- <PERSON><PERSON> script se ejecuta automáticamente al crear el contenedor

-- Crear la base de datos si no existe
SELECT 'CREATE DATABASE imserso'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'imserso')\gexec

-- Conectar a la base de datos
\c imserso;

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Crear usuario de aplicación
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'imserso_app') THEN
        CREATE ROLE imserso_app WITH LOGIN PASSWORD 'app_password';
    END IF;
END
$$;

-- <PERSON><PERSON><PERSON> permisos
GRANT CONNECT ON DATABASE imserso TO imserso_app;
GRANT USAGE ON SCHEMA public TO imserso_app;
GRANT CREATE ON SCHEMA public TO imserso_app;

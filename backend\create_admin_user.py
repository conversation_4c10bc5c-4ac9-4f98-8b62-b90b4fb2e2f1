#!/usr/bin/env python3
"""
Script para crear usuario administrador inicial
"""

import asyncio
import asyncpg
from passlib.context import CryptContext

# Configuración
DATABASE_URL = "postgresql://imserso_user:imserso_pass@localhost:5432/imserso"
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def create_admin_user():
    """Crear usuario administrador inicial"""
    
    # Datos del admin
    username = "<EMAIL>"
    email = "<EMAIL>"
    full_name = "Administrador IMSERSO"
    password = "admin123"
    
    # Hash de la contraseña
    hashed_password = pwd_context.hash(password)
    
    try:
        # Conectar a la base de datos
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Crear tabla users si no existe
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                full_name VA<PERSON>HA<PERSON>(255),
                hashed_password VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                is_superuser BOOLEAN DEFAULT false,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insertar usuario admin
        await conn.execute('''
            INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser) 
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (username) DO UPDATE SET
                hashed_password = EXCLUDED.hashed_password,
                updated_at = CURRENT_TIMESTAMP
        ''', username, email, full_name, hashed_password, True, True)
        
        print("✅ Usuario administrador creado exitosamente")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        print(f"   Email: {email}")
        
        # Verificar
        user = await conn.fetchrow('SELECT * FROM users WHERE username = $1', username)
        if user:
            print(f"✅ Verificación: Usuario {user['username']} existe en la base de datos")
        else:
            print("❌ Error: Usuario no encontrado después de la creación")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Error creando usuario administrador: {e}")

if __name__ == "__main__":
    asyncio.run(create_admin_user())

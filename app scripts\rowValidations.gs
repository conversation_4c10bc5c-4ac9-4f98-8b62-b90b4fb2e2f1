/* This file contains all row based validations
  A row based validation is a function that receives:
  - htmlObj : An html obj to report all the errors to.
  - rowValues: List of values for the current row
  - rowIndexInSheet: This is the absolute index of the row in the sheet. Keep in mind that row number 1 in the
                     sheet is actually index 0 inside the matrix value (A.K.A DataRange).
*/

function validateAvailableHotelRooms(errorManager, rowValues, rowIndexInSheet, hotelMapper){
  var hotel1Id = rowValues[headers.CodHotel1Destino];
  var hotel2Id = rowValues[headers.CodHotel2Destino];
  var accomodations = rowValues[headers.NumTotalPlazas];
  var hotel1 = hotelMapper[hotel1Id];
  if (hotel1 == undefined){
    errorManager
      .addRowError(rowIndexInSheet, `Parece que el hotel destino 1 ${hotel1Id} no está disponible en la lista de hoteles`)
      .addRowButton(headers.CodHotel1Destino+1, "CodHotel1Destino");
  }else if (accomodations > hotel1.totalRooms){
    errorManager
      .addRowError(rowIndexInSheet, `El hotel destino 1 ${hotel1Id} que has solicitado no puede albergar tantos clientes (solicitados: ${accomodations}, máximo hotel: ${hotel1.totalRooms})`)
      .addRowButton(headers.CodHotel1Destino+1, "CodHotel1Destino")
      .addRowButton(headers.NumTotalPlazas+1, "NumTotalPlazas");
  }
  if(hotel2Id != ""){
    var hotel2 = hotelMapper[hotel2Id];
    if (hotel2 == undefined){
      errorManager
        .addRowError(rowIndexInSheet, `Parece que el hotel destino 2 ${hotel2Id} no está disponible en la lista de hoteles`)
        .addRowButton(headers.CodHotel2Destino+1, "CodHotel2Destino");
    }else if (accomodations > hotel2.totalRooms){
      errorManager
        .addRowError(rowIndexInSheet, "El hotel destino 2 que has solicitado no puede albergar tantos clientes")
        .addRowButton(headers.CodHotel2Destino+1, "CodHotel2Destino")
        .addRowButton(headers.NumTotalPlazas+1, "NumTotalPlazas");
    }  
  }
}

function validateServiceOnTrip(errorManager, rowValues, rowIndexInSheet){
  var HoraInicioIda = rowValues[headers.HoraInicioIda];
  var HoraFinIda = rowValues[headers.HoraFinIda];
  if ((HoraInicioIda < 1400 && HoraFinIda > 1501) || (HoraInicioIda > 1501 && HoraFinIda > 2201)){
    var Serv1RutaIda = rowValues[headers.Serv1RutaIda];
    if (!Serv1RutaIda || Serv1RutaIda == ""){
      errorManager
        .addRowError(rowIndexInSheet, "Debes establecer un servicio en la ruta de ida para esas horas de Inicio/Final")
        .addRowButton(headers.Serv1RutaIda+1, "Serv1RutaIda");
    }
  }
  var HoraInicioVuelta = rowValues[headers.HoraInicioVuelta];
  var HoraFinVuelta = rowValues[headers.HoraFinVuelta];
  if ((HoraInicioVuelta < 1400 && HoraFinVuelta > 1501) || (HoraInicioVuelta > 1501 && HoraFinVuelta > 2201)){
    var Serv1RutaVuelta = rowValues[headers.Serv1RutaVuelta];
    if (!Serv1RutaVuelta || Serv1RutaVuelta == ""){
      errorManager
        .addRowError(rowIndexInSheet, "Debes establecer un servicio en la ruta de vuelta para esas horas de Inicio/Final")
        .addRowButton(headers.Serv1RutaVuelta+1, "Serv1RutaVuelta")
    }
  }
}

/* Función para validar la hora de fin ida
HORAFINIDA no puede ser posterior a las 22:00 sí solo hay UN campo de transporte de ida.
*/
function validateArrivalTime(errorManager, rowValues, rowIndexInSheet){
  var trans = [headers.Trans1Ida, headers.Trans2Ida, headers.Trans3Ida, headers.Trans4Ida]
  var trans_list = trans.map((id)=>{return rowValues[id]}).filter((el) => {return el != "";});
  if (trans_list.length == 1 && rowValues[headers.HoraFinIda] > 2200){
    errorManager
      .addRowError(rowIndexInSheet, "La hora de fin ida no puede ser posterior a las 22:00 si solo hay un transporte establecido")
      .addRowButton(headers.HoraFinIda+1, "HoraFinIda")
  }
}

/* Función para validar que el último transporte sea siempre un Autocar
¿Podemos controlar que el último transporte de ida de una línea sea siempre una A (autocar)?
*/
function validateLastTransportShouldBeBus(errorManager, rowValues, rowIndexInSheet){
  var trans_to_check = [headers.Trans1Ida, headers.Trans2Ida, headers.Trans3Ida, headers.Trans4Ida];
  var trans = trans_to_check.map((id)=>{return rowValues[id]});
  var last_trans = null;
  var last_id = null;
  for (i=0; i < trans.length; i++){
    if (!trans[i]){
      break;
    }
    last_trans = trans[i];
    last_id = i;
  }
  if (last_trans != null && last_id != null){
    if (last_trans != "A"){
      errorManager
        .addRowError(rowIndexInSheet, "El último transporte ha de ser siempre un A (autocar)")
        .addRowButton(trans_to_check[last_id]+1, "Ir a último transporte");
    }
  }
}

/* Función para validar que el primer transporte de vuelta sea siempre un Autocar
 */
function validateFirstTransportShouldBeBus(errorManager, rowValues, rowIndexInsheet){
  var transVuelta = rowValues[headers.Trans1Vuelta];
  var tipoTurno = rowValues[headers.TipoTurno];
  if (transVuelta != 'A' && tipoTurno == 'T'){
      errorManager
	  .addRowError(rowIndexInsheet, "El primer transporte de vuelta ha de ser siempre un A (autocar)")
	  .addRowButton(headers.Trans1Vuelta+1, "Trans1Vuelta");
  }
}

/* Función para validas los tiempos de vuelta
*/
function validateReturnDepartureTime(errorManager, rowValues, rowIndexInSheet, sheetType){
  var HoraInicioVuelta = rowValues[headers.HoraInicioVuelta];
  var TipoTurno = rowValues[headers.TipoTurno];
  if (TipoTurno == 'T'){
    if (HoraInicioVuelta < 600){
      errorManager
        .addRowError(rowIndexInSheet, "La hora de inicio vuelta no puede ser anterior a las 6:00")
        .addRowButton(headers.HoraInicioVuelta+1, "HoraInicioVuelta");
    }else if(HoraInicioVuelta > 2200){
      errorManager
        .addRowError(rowIndexInSheet, "La hora de inicio vuelta no puede ser posterior a las 22:00")
        .addRowButton(headers.HoraInicioVuelta+1, "HoraInicioVuelta");
    }
  }
}

/* Función para validar los tiempos de salida siguientes
HORAINICIO2IDA. Nunca antes de las 06:00. Ni de HORAINICIOIDA. Sucesivos para el resto.
HORAINICIOVUELTA. Nunca antes de las 06:00, ni posterior a las 22:00.
*/
function validateNextDepartureTime(errorManager, rowValues, rowIndexInSheet, sheetType){
  var minHour = 600;
  if (sheetType == 'L3'){
    minHour = 800;
  }
  if (rowValues[headers.TipoTurno] == "T"){
    var HoraInicioIda = rowValues[headers.HoraInicioIda];
    var HoraInicio2Ida = rowValues[headers.HoraInicio2Ida];
    var HoraInicio3Ida = rowValues[headers.HoraInicio3Ida];
    var HoraInicio4Ida = rowValues[headers.HoraInicio4Ida];
    if (HoraInicio2Ida){
      if (HoraInicio2Ida < minHour){
        errorManager
          .addRowError(rowIndexInSheet, "La hora de inicio 2 ida no puede ser anterior a las " + minHour)
          .addRowButton(headers.HoraInicio2Ida+1, "HoraInicio2Ida");
      }
      if (HoraInicio2Ida < HoraInicioIda){
        errorManager
          .addRowError(rowIndexInSheet, "La hora de inicio 2 ida no puede ser anterior a la hora de inicio")
          .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
          .addRowButton(headers.HoraInicio2Ida+1, "HoraInicio2Ida");
      }
      if (HoraInicio3Ida){
        if(HoraInicio3Ida < HoraInicio2Ida){
          errorManager
            .addRowError(rowIndexInSheet, "La hora de inicio 3 ida no puede ser anterior a la hora de inicio 2 ida")
            .addRowButton(headers.HoraInicio2Ida+1, "HoraInicio2Ida")
            .addRowButton(headers.HoraInicio3Ida+1, "HoraInicio3Ida");
        }
        if (HoraInicio4Ida){
          if(HoraInicio4Ida < HoraInicio3Ida){
            errorManager
            .addRowError(rowIndexInSheet, "La hora de inicio 4 ida no puede ser anterior a la hora de inicio 3 ida")
            .addRowButton(headers.HoraInicio3Ida+1, "HoraInicio3Ida")
            .addRowButton(headers.HoraInicio4Ida+1, "HoraInicio4Ida");
          }
        }
      }
    }
  }
}

/* Función para validar los tiempos de salida
Cumple con las condiciones de: 
- HORAINICIOIDA. No antes de las 06:00 salvo que TRANS1IDA sea “A” y TRANS2IDA no esté vacío. Nunca antes de las 06:00, ni después de las 20.
- HORAINICIOIDA debe cumplimentarse obligatoriamente si TIPODETURNO es “T” y debe estar vacío si es “S”
*/
function validateDepartureTime(errorManager, rowValues, rowIndexInSheet, sheetType){
  var minHour = 600;
  if (sheetType == 'L3'){
    minHour = 800;
  }
  if (rowValues[headers.TipoTurno] == "T"){
    if (!rowValues[headers.Trans1Ida]){
      errorManager
        .addRowError(rowIndexInSheet, "Debes especificar un transporte de ida")
        .addRowButton(headers.Trans1Ida+1, "Trans1Ida")
    }
    if (!rowValues[headers.Trans1Vuelta]){
      errorManager
        .addRowError(rowIndexInSheet, "Debers espeficiar un transporte de vuelta")
        .addRowButton(headers.Trans1Vuelta+1, "Trans1Vuelta")
    }
    if (!rowValues[headers.HoraInicioIda]){
      errorManager
        .addRowError(rowIndexInSheet, "Debes especificar una hora de inicio ida")
        .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
    }else{
      if (rowValues[headers.HoraInicioIda] < minHour){
        errorManager
          .addRowError(rowIndexInSheet, "Hora de inicio ida demasiado pronto")
          .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
      }else if (rowValues[headers.HoraInicioIda] > 2200){
        errorManager
          .addRowError(rowIndexInSheet, "Hora de inicio ida demasiado tarde")
          .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
      }else if (rowValues[headers.HoraInicioIda] < minHour){
        if(rowValues[headers.Trans1Ida] != "V"){
          errorManager
            .addRowError(rowIndexInSheet, "Hora de inicio ida no puede ser antes de las 8:00 a no ser que el transporte sea avión (V)")
            .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
            .addRowButton(headers.Trans1Ida+1, "Trans1Ida")
        }else if(rowValues[headers.Trans1Ida] == "V" && !rowValues[headers.Trans2Ida]){
          errorManager
            .addRowError(rowIndexInSheet, "La hora de inicio ida no puede ser antes de las 8:00 a no ser que el transporte sea avión (V) y haya otra salida")
            .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
            .addRowButton(headers.Trans1Ida+1, "Trans1Ida")
            .addRowButton(headers.Trans2Ida+1, "Trans2Ida");
        };
      }
    }
  }else if (rowValues[headers.TipoTurno] == "S"){
    if (rowValues[headers.HoraInicioIda] != ""){
      errorManager
        .addRowError(rowIndexInSheet, "Cuando el tipo de turno es S, debes dejar la hora de inicio ida vacía")
        .addRowButton(headers.HoraInicioIda+1, "HoraInicioIda")
        .addRowButton(headers.TipoTurno+1, "TipoTurno");
    }
  }
}

/* Funcion para validar los kms de ida y de vuelta */
function validateTripKms(errorManager, rowValues, rowIndexInSheet){
  var IdaSet = new Set([rowValues[headers.Trans1Ida], rowValues[headers.Trans2Ida], rowValues[headers.Trans3Ida], rowValues[headers.Trans4Ida]].join('').split(''));
  var VueltaSet = new Set([rowValues[headers.Trans1Vuelta], rowValues[headers.Trans2Vuelta], rowValues[headers.Trans3Vuelta], rowValues[headers.Trans4Vuelta]].join('').split(''));
  var onlyBusVuelta = VueltaSet.size == 1 && VueltaSet.has('A');
  var onlyBusIda = IdaSet.size == 1 && IdaSet.has('A');
  if (rowValues[headers.TotalKmVuelta] > 499 && onlyBusVuelta){
    errorManager
      .addRowError(rowIndexInSheet, "El total de kms de vuelta excede el límite de 499kms")
      .addRowButton(headers.TotalKmVuelta+1, "TotalKmVuelta");
  }
  if (rowValues[headers.TotalKmIda] > 499 && onlyBusIda){
    errorManager
      .addRowError(rowIndexInSheet, "El total de kms de ida excede el límite de 499kms")
      .addRowButton(headers.TotalKmIda+1, "TotalKmIda");
  }
}

# Configuración del Sistema IMSERSO

# Configuración básica
PROJECT_NAME="Sistema IMSERSO"
VERSION="1.0.0"
ENVIRONMENT="development"
DEBUG=true

# Configuración de la API
API_V1_STR="/api/v1"
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# Configuración de CORS
CORS_ORIGINS="http://localhost:3000,http://localhost:8080"
ALLOWED_HOSTS="localhost,127.0.0.1"

# Configuración de base de datos PostgreSQL
POSTGRES_SERVER="postgres"
POSTGRES_USER="imserso_user"
POSTGRES_PASSWORD="imserso_pass"
POSTGRES_DB="imserso"
POSTGRES_PORT="5432"

# Configuración de Redis
REDIS_URL="redis://redis:6379/0"

# APIs de IA para procesamiento de PDFs
GEMINI_API_KEY=""
OPENAI_API_KEY=""
ANTHROPIC_API_KEY=""

# Configuración de archivos
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_DIR="/app/storage/uploads"
EXPORT_DIR="/app/storage/exports"
TEMP_DIR="/app/storage/temp"

# Configuración de email
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=""
SMTP_USER=""
SMTP_PASSWORD=""
EMAILS_FROM_EMAIL=""
EMAILS_FROM_NAME="Sistema IMSERSO"

# Configuración de logs
LOG_LEVEL="INFO"
LOG_FILE="/app/logs/imserso.log"

# Configuración de seguridad
BCRYPT_ROUNDS=12

# APIs de mapas para cálculo de distancias reales (GRATUITAS)
# OpenRouteService - Registro gratuito en https://openrouteservice.org/
# 2000 requests/día gratis
OPENROUTE_API_KEY=""

# GraphHopper - Registro gratuito en https://www.graphhopper.com/
# 2500 requests/día gratis
GRAPHHOPPER_API_KEY=""

# APIs externas adicionales (opcionales)
GOOGLE_MAPS_API_KEY=""
MAPBOX_API_KEY=""

# Configuración de validaciones
VALIDATION_RULES_CACHE_TTL=3600
MAX_CONCURRENT_VALIDATIONS=10

# Configuración de generación de viajes
MAX_TRIPS_PER_BATCH=1000
TRIP_GENERATION_TIMEOUT=300

# Configuración de exportación
MAX_EXPORT_RECORDS=100000
EXPORT_CACHE_TTL=1800

# ========================================
# INSTRUCCIONES PARA OBTENER API KEYS
# ========================================

# 1. OpenRouteService (RECOMENDADO - MÁS PRECISO)
# - Ir a: https://openrouteservice.org/dev/#/signup
# - Crear cuenta gratuita
# - Obtener API key
# - Límite: 2000 requests/día
# - Copiar la key en OPENROUTE_API_KEY

# 2. GraphHopper (ALTERNATIVO)
# - Ir a: https://www.graphhopper.com/
# - Crear cuenta gratuita
# - Obtener API key
# - Límite: 2500 requests/día
# - Copiar la key en GRAPHHOPPER_API_KEY

# NOTA: El sistema funciona sin API keys usando cálculos aproximados,
# pero las distancias reales mejoran significativamente la precisión
# de la optimización de rutas.

# ========================================
# CONFIGURACIÓN PARA DESARROLLO
# ========================================

# Para desarrollo local, puedes usar:
# POSTGRES_SERVER=localhost
# REDIS_URL=redis://localhost:6379/0

# Para Docker Compose (recomendado):
# POSTGRES_SERVER=postgres
# REDIS_URL=redis://redis:6379/0

/**
 * This file contains a helper class for being able to
 * retrieve the province name for a given code and
 * viceversa.
 */

class ProvincesMap{
  constructor(dataMatrix){
    const codeColIndex = 0;
    const nameColIndex = 1;
    this._nameToCode = {};
    this._codeToName = {};
    dataMatrix.forEach(row => {
      this._nameToCode[row[nameColIndex].toLowerCase()] = row[codeColIndex];
      this._codeToName[row[codeColIndex]] = row[nameColIndex];
    });
  }

  nameToCode(name){
    return this._nameToCode[name.toLowerCase()];
  }

  codeToName(code){
    return this._codeToName[code];
  }
}


function getProvincesMap(sheetName){
  if (!sheetName){
    sheetName = settings.provinceSheetName;
  }
  Logger.log("Loading provinces");
  var provincesMatrix = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName).getRange(settings.provicesMapRange).getValues();
  Logger.log("Provinces loaded");
  return new ProvincesMap(provincesMatrix);
}

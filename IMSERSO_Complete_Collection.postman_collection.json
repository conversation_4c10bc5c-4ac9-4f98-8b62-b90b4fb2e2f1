{
	"info": {
		"_postman_id": "imserso-api-2025",
		"name": "🚀 Sistema IMSERSO API - Colección Completa",
		"description": "Colección completa para probar todas las funcionalidades del Sistema IMSERSO\n\n## 🎯 Funcionalidades principales:\n- 🏥 Health Check y estado del sistema\n- 🔐 Autenticación y gestión de usuarios\n- 📊 Dashboard y métricas en tiempo real\n- 📋 Procesamiento de pliegos con IA\n- 🏨 Gestión completa de hoteles\n- 🚌 Gestión de transportes (vuelos, trenes, autocares)\n- ✈️ **Generación automática de viajes optimizados**\n- ✅ **Validaciones con 47+ reglas IMSERSO**\n- 📤 Exportación de archivos OUTCOME\n\n## 🔧 Configuración:\n1. Importa esta colección en Postman\n2. Configura el environment con base_url = http://localhost:8000\n3. Ejecuta Login para obtener el token automáticamente\n4. ¡Prueba todas las funcionalidades!\n\n## 🚀 Flujo recomendado:\n1. Health Check\n2. Login\n3. Importar hoteles/transportes\n4. Generar viajes\n5. Validar resultados\n6. Exportar archivos",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"item": [
		{
			"name": "🏥 Health & System",
			"description": "Endpoints para verificar el estado del sistema",
			"item": [
				{
					"name": "Health Check",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"pm.test('Status is healthy', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.status).to.eql('healthy');",
									"});",
									"",
									"pm.test('Database is connected', function () {",
									"    const response = pm.response.json();",
									"    pm.expect(response.database.status).to.eql('connected');",
									"});"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/api/v1/health",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "health"]
						},
						"description": "Verifica el estado del sistema, conexión a PostgreSQL y timestamp actual"
					}
				},
				{
					"name": "API Root Info",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{base_url}}/",
							"host": ["{{base_url}}"]
						},
						"description": "Información básica de la API y enlaces útiles"
					}
				}
			]
		},
		{
			"name": "🔐 Autenticación",
			"description": "Gestión de usuarios y autenticación JWT",
			"item": [
				{
					"name": "Login",
					"event": [
						{
							"listen": "test",
							"script": {
								"exec": [
									"if (pm.response.code === 200) {",
									"    const response = pm.response.json();",
									"    pm.environment.set('access_token', response.access_token);",
									"    pm.environment.set('token_type', response.token_type);",
									"    pm.test('Token received', function () {",
									"        pm.expect(response.access_token).to.not.be.undefined;",
									"    });",
									"}"
								],
								"type": "text/javascript"
							}
						}
					],
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/x-www-form-urlencoded"
							}
						],
						"body": {
							"mode": "urlencoded",
							"urlencoded": [
								{
									"key": "username",
									"value": "<EMAIL>",
									"type": "text",
									"description": "Usuario administrador por defecto"
								},
								{
									"key": "password",
									"value": "admin123",
									"type": "text",
									"description": "Contraseña por defecto"
								}
							]
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/login",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "login"]
						},
						"description": "Autenticación con usuario y contraseña. Guarda automáticamente el token JWT."
					}
				},
				{
					"name": "Crear Usuario",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							},
							{
								"key": "Authorization",
								"value": "{{token_type}} {{access_token}}"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n    \"username\": \"test_user\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"Usuario de Prueba\",\n    \"password\": \"test123\",\n    \"is_active\": true,\n    \"is_superuser\": false\n}"
						},
						"url": {
							"raw": "{{base_url}}/api/v1/auth/register",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "register"]
						},
						"description": "Crear un nuevo usuario en el sistema"
					}
				},
				{
					"name": "Mi Perfil",
					"request": {
						"method": "GET",
						"header": [
							{
								"key": "Authorization",
								"value": "{{token_type}} {{access_token}}"
							}
						],
						"url": {
							"raw": "{{base_url}}/api/v1/auth/me",
							"host": ["{{base_url}}"],
							"path": ["api", "v1", "auth", "me"]
						},
						"description": "Obtener información del usuario autenticado"
					}
				}
			]
		}

"""
Modelo de usuario
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class User(BaseModel):
    """Modelo de usuario del sistema"""
    
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(255))
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Relaciones
    viajes_creados = relationship("Viaje", back_populates="created_by_user")
    # exportaciones = relationship("Exportacion", back_populates="created_by_user")  # TODO: Crear modelo Exportacion
    # logs = relationship("LogSistema", back_populates="usuario")  # TODO: Crear modelo LogSistema
    
    def __repr__(self) -> str:
        return f"<User(username='{self.username}', email='{self.email}')>"

"""
Esquemas Pydantic para usuarios
"""

from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr


class UserBase(BaseModel):
    """Esquema base de usuario"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    """Esquema para crear usuario"""
    password: str


class UserUpdate(BaseModel):
    """Esquema para actualizar usuario"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None


class UserInDB(UserBase):
    """Esquema de usuario en base de datos"""
    id: UUID
    hashed_password: str
    is_superuser: bool = False
    
    class Config:
        from_attributes = True


class User(UserBase):
    """Esquema de usuario para respuestas"""
    id: UUID
    is_superuser: bool = False
    
    class Config:
        from_attributes = True


class Token(BaseModel):
    """Esquema de token de acceso"""
    access_token: str
    token_type: str
    expires_in: int


class TokenPayload(BaseModel):
    """Payload del token JWT"""
    sub: Optional[str] = None

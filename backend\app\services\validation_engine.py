"""
Motor de validaciones IMSERSO
Implementa todas las reglas de validación identificadas del sistema actual
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import time
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from loguru import logger

from app.core.database import AsyncSessionLocal
from app.core.exceptions import ValidationError, BusinessRuleError
from app.models.viaje import Viaje
from app.models.validation import ReglaValidacion, ValidacionViaje
from app.models.master_data import DistribucionPlaza, Aeropuerto
from app.models.hotel import Hotel


class ValidationEngine:
    """Motor de validaciones para viajes IMSERSO"""
    
    def __init__(self):
        self.validation_rules = {}
        self.error_messages = {}
    
    async def validate_trip(self, viaje: Viaje) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Validar un viaje individual contra todas las reglas
        
        Returns:
            Tuple[bool, List[Dict]]: (es_valido, lista_de_errores)
        """
        
        errors = []
        
        try:
            # 1. Validaciones de transporte
            transport_errors = await self._validate_transport_rules(viaje)
            errors.extend(transport_errors)
            
            # 2. Validaciones de horarios
            time_errors = await self._validate_time_rules(viaje)
            errors.extend(time_errors)
            
            # 3. Validaciones de servicios
            service_errors = await self._validate_service_rules(viaje)
            errors.extend(service_errors)
            
            # 4. Validaciones de kilómetros
            km_errors = await self._validate_km_rules(viaje)
            errors.extend(km_errors)
            
            # 5. Validaciones de hoteles
            hotel_errors = await self._validate_hotel_rules(viaje)
            errors.extend(hotel_errors)
            
            # 6. Validaciones de campos obligatorios
            required_errors = await self._validate_required_fields(viaje)
            errors.extend(required_errors)
            
            is_valid = len(errors) == 0
            
            if is_valid:
                logger.debug(f"✅ Viaje {viaje.num_reg} válido")
            else:
                logger.warning(f"❌ Viaje {viaje.num_reg} con {len(errors)} errores")
            
            return is_valid, errors
            
        except Exception as e:
            logger.error(f"❌ Error validando viaje {viaje.num_reg}: {e}")
            return False, [{"rule": "VALIDATION_ERROR", "message": f"Error interno: {str(e)}"}]
    
    async def validate_batch(self, lote: str, temporada: str = "2025") -> Dict[str, Any]:
        """
        Validar un lote completo de viajes
        """
        
        logger.info(f"🔍 Iniciando validación de lote {lote} - {temporada}")
        
        async with AsyncSessionLocal() as session:
            # Obtener todos los viajes del lote
            query = select(Viaje).where(
                and_(
                    Viaje.lote == lote,
                    Viaje.temporada == temporada
                )
            )
            
            result = await session.execute(query)
            viajes = result.scalars().all()
            
            if not viajes:
                return {
                    "lote": lote,
                    "total_viajes": 0,
                    "viajes_validos": 0,
                    "viajes_con_errores": 0,
                    "errores_por_regla": {},
                    "validaciones_adicionales": {}
                }
            
            # Validar cada viaje
            total_errors = []
            valid_count = 0
            error_count = 0
            
            for viaje in viajes:
                is_valid, errors = await self.validate_trip(viaje)
                
                if is_valid:
                    valid_count += 1
                else:
                    error_count += 1
                    total_errors.extend(errors)
                
                # Guardar resultados de validación
                await self._save_validation_results(session, viaje, errors)
            
            # Validaciones adicionales a nivel de lote
            batch_validations = await self._validate_batch_rules(session, viajes, lote)
            
            # Agrupar errores por regla
            errors_by_rule = {}
            for error in total_errors:
                rule = error.get("rule", "UNKNOWN")
                if rule not in errors_by_rule:
                    errors_by_rule[rule] = 0
                errors_by_rule[rule] += 1
            
            await session.commit()
            
            result = {
                "lote": lote,
                "total_viajes": len(viajes),
                "viajes_validos": valid_count,
                "viajes_con_errores": error_count,
                "errores_por_regla": errors_by_rule,
                "validaciones_adicionales": batch_validations
            }
            
            logger.info(f"✅ Validación completada: {valid_count}/{len(viajes)} viajes válidos")
            return result
    
    async def _validate_transport_rules(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar reglas de transporte"""
        
        errors = []
        
        if viaje.tipo_turno == 'T':  # Solo para viajes con transporte
            
            # 1. Primer transporte de vuelta debe ser autocar
            if viaje.trans_1_vuelta and viaje.trans_1_vuelta != 'A':
                errors.append({
                    "rule": "FIRST_TRANSPORT_BUS",
                    "message": "El primer transporte de vuelta ha de ser siempre un A (autocar)",
                    "field": "trans_1_vuelta",
                    "value": viaje.trans_1_vuelta
                })
            
            # 2. Último transporte de ida debe ser autocar
            transportes_ida = [viaje.trans_1_ida, viaje.trans_2_ida, viaje.trans_3_ida, viaje.trans_4_ida]
            transportes_ida = [t for t in transportes_ida if t]  # Filtrar nulos
            
            if transportes_ida and transportes_ida[-1] != 'A':
                errors.append({
                    "rule": "LAST_TRANSPORT_BUS",
                    "message": "El último transporte ha de ser siempre un A (autocar)",
                    "field": "ultimo_transporte_ida",
                    "value": transportes_ida[-1]
                })
            
            # 3. Transportes obligatorios
            if not viaje.trans_1_ida:
                errors.append({
                    "rule": "REQUIRED_TRANSPORT_IDA",
                    "message": "Debes especificar un transporte de ida",
                    "field": "trans_1_ida"
                })
            
            if not viaje.trans_1_vuelta:
                errors.append({
                    "rule": "REQUIRED_TRANSPORT_VUELTA",
                    "message": "Debes especificar un transporte de vuelta",
                    "field": "trans_1_vuelta"
                })
        
        return errors
    
    async def _validate_time_rules(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar reglas de horarios"""
        
        errors = []
        
        if viaje.tipo_turno == 'T':
            
            # Hora mínima según lote
            hora_minima = time(8, 0) if viaje.lote == 'L3' else time(6, 0)
            hora_maxima = time(22, 0)
            
            # 1. Validar hora de inicio ida
            if not viaje.hora_inicio_ida:
                errors.append({
                    "rule": "REQUIRED_DEPARTURE_TIME",
                    "message": "Debes especificar una hora de inicio ida",
                    "field": "hora_inicio_ida"
                })
            else:
                # Hora mínima
                if viaje.hora_inicio_ida < hora_minima:
                    # Excepción: puede ser antes si es vuelo Y hay segundo transporte
                    if not (viaje.trans_1_ida == 'V' and viaje.trans_2_ida):
                        errors.append({
                            "rule": "DEPARTURE_TIME_TOO_EARLY",
                            "message": f"Hora de inicio ida demasiado pronto (mínimo {hora_minima.strftime('%H:%M')})",
                            "field": "hora_inicio_ida",
                            "value": viaje.hora_inicio_ida.strftime('%H:%M')
                        })
                
                # Hora máxima
                if viaje.hora_inicio_ida > hora_maxima:
                    errors.append({
                        "rule": "DEPARTURE_TIME_TOO_LATE",
                        "message": "Hora de inicio ida demasiado tarde",
                        "field": "hora_inicio_ida",
                        "value": viaje.hora_inicio_ida.strftime('%H:%M')
                    })
            
            # 2. Validar secuencia de horarios ida
            horarios_ida = [
                (viaje.hora_inicio_ida, "hora_inicio_ida"),
                (viaje.hora_inicio_2_ida, "hora_inicio_2_ida"),
                (viaje.hora_inicio_3_ida, "hora_inicio_3_ida"),
                (viaje.hora_inicio_4_ida, "hora_inicio_4_ida")
            ]
            
            prev_time = None
            for hora, field_name in horarios_ida:
                if hora:
                    if hora < hora_minima:
                        errors.append({
                            "rule": "TIME_SEQUENCE_TOO_EARLY",
                            "message": f"La {field_name.replace('_', ' ')} no puede ser anterior a las {hora_minima.strftime('%H:%M')}",
                            "field": field_name,
                            "value": hora.strftime('%H:%M')
                        })
                    
                    if prev_time and hora < prev_time:
                        errors.append({
                            "rule": "TIME_SEQUENCE_INVALID",
                            "message": f"La {field_name.replace('_', ' ')} no puede ser anterior a la hora anterior",
                            "field": field_name,
                            "value": hora.strftime('%H:%M')
                        })
                    
                    prev_time = hora
            
            # 3. Validar hora de fin ida (solo si hay un transporte)
            transportes_ida_count = sum(1 for t in [viaje.trans_1_ida, viaje.trans_2_ida, viaje.trans_3_ida, viaje.trans_4_ida] if t)
            
            if transportes_ida_count == 1 and viaje.hora_fin_ida and viaje.hora_fin_ida > hora_maxima:
                errors.append({
                    "rule": "ARRIVAL_TIME_TOO_LATE",
                    "message": "La hora de fin ida no puede ser posterior a las 22:00 si solo hay un transporte establecido",
                    "field": "hora_fin_ida",
                    "value": viaje.hora_fin_ida.strftime('%H:%M')
                })
            
            # 4. Validar horarios de vuelta
            if viaje.hora_inicio_vuelta:
                if viaje.hora_inicio_vuelta < time(6, 0):
                    errors.append({
                        "rule": "RETURN_TIME_TOO_EARLY",
                        "message": "La hora de inicio vuelta no puede ser anterior a las 6:00",
                        "field": "hora_inicio_vuelta",
                        "value": viaje.hora_inicio_vuelta.strftime('%H:%M')
                    })
                
                if viaje.hora_inicio_vuelta > hora_maxima:
                    errors.append({
                        "rule": "RETURN_TIME_TOO_LATE",
                        "message": "La hora de inicio vuelta no puede ser posterior a las 22:00",
                        "field": "hora_inicio_vuelta",
                        "value": viaje.hora_inicio_vuelta.strftime('%H:%M')
                    })
        
        elif viaje.tipo_turno == 'S':
            # Para viajes sin transporte, no debe haber hora de inicio
            if viaje.hora_inicio_ida:
                errors.append({
                    "rule": "NO_TIME_FOR_NO_TRANSPORT",
                    "message": "No debe especificar hora de inicio para viajes sin transporte",
                    "field": "hora_inicio_ida"
                })
        
        return errors

    async def _validate_service_rules(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar reglas de servicios en ruta"""

        errors = []

        if viaje.tipo_turno == 'T' and viaje.hora_inicio_ida and viaje.hora_fin_ida:

            # Regla: Se requiere servicio si:
            # (HoraInicioIda < 14:00 Y HoraFinIda > 15:01) O (HoraInicioIda > 15:01 Y HoraFinIda > 22:01)

            inicio = viaje.hora_inicio_ida
            fin = viaje.hora_fin_ida

            requiere_servicio_ida = (
                (inicio < time(14, 0) and fin > time(15, 1)) or
                (inicio > time(15, 1) and fin > time(22, 1))
            )

            if requiere_servicio_ida and not viaje.serv_1_ruta_ida:
                errors.append({
                    "rule": "SERVICE_REQUIRED_IDA",
                    "message": "Debes establecer un servicio en la ruta de ida para esas horas de Inicio/Final",
                    "field": "serv_1_ruta_ida",
                    "details": {
                        "hora_inicio": inicio.strftime('%H:%M'),
                        "hora_fin": fin.strftime('%H:%M')
                    }
                })

        # Validar servicios de vuelta si aplica
        if viaje.tipo_turno == 'T' and viaje.hora_inicio_vuelta and viaje.hora_fin_vuelta:

            inicio_vuelta = viaje.hora_inicio_vuelta
            fin_vuelta = viaje.hora_fin_vuelta

            requiere_servicio_vuelta = (
                (inicio_vuelta < time(14, 0) and fin_vuelta > time(15, 1)) or
                (inicio_vuelta > time(15, 1) and fin_vuelta > time(22, 1))
            )

            if requiere_servicio_vuelta and not viaje.serv_1_ruta_vuelta:
                errors.append({
                    "rule": "SERVICE_REQUIRED_VUELTA",
                    "message": "Debes establecer un servicio en la ruta de vuelta para esas horas de Inicio/Final",
                    "field": "serv_1_ruta_vuelta",
                    "details": {
                        "hora_inicio": inicio_vuelta.strftime('%H:%M'),
                        "hora_fin": fin_vuelta.strftime('%H:%M')
                    }
                })

        return errors

    async def _validate_km_rules(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar reglas de kilómetros"""

        errors = []

        if viaje.tipo_turno == 'T':

            # Verificar si solo hay autocares en ida
            transportes_ida = [viaje.trans_1_ida, viaje.trans_2_ida, viaje.trans_3_ida, viaje.trans_4_ida]
            transportes_ida = [t for t in transportes_ida if t]

            if transportes_ida and all(t == 'A' for t in transportes_ida):
                if viaje.total_km_ida and viaje.total_km_ida > 499:
                    errors.append({
                        "rule": "KM_LIMIT_IDA_BUS_ONLY",
                        "message": "El total de kms de ida excede el límite de 499kms",
                        "field": "total_km_ida",
                        "value": viaje.total_km_ida
                    })

            # Verificar si solo hay autocares en vuelta
            transportes_vuelta = [viaje.trans_1_vuelta, viaje.trans_2_vuelta, viaje.trans_3_vuelta, viaje.trans_4_vuelta]
            transportes_vuelta = [t for t in transportes_vuelta if t]

            if transportes_vuelta and all(t == 'A' for t in transportes_vuelta):
                if viaje.total_km_vuelta and viaje.total_km_vuelta > 499:
                    errors.append({
                        "rule": "KM_LIMIT_VUELTA_BUS_ONLY",
                        "message": "El total de kms de vuelta excede el límite de 499kms",
                        "field": "total_km_vuelta",
                        "value": viaje.total_km_vuelta
                    })

        return errors

    async def _validate_hotel_rules(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar reglas de hoteles"""

        errors = []

        async with AsyncSessionLocal() as session:

            # 1. Validar que el hotel destino 1 existe
            if viaje.hotel_destino_1:
                result = await session.execute(
                    select(Hotel).where(Hotel.codigo == viaje.hotel_destino_1)
                )
                hotel1 = result.scalar_one_or_none()

                if not hotel1:
                    errors.append({
                        "rule": "HOTEL_NOT_FOUND",
                        "message": f"Parece que el hotel destino 1 {viaje.hotel_destino_1} no está disponible en la lista de hoteles",
                        "field": "hotel_destino_1",
                        "value": viaje.hotel_destino_1
                    })
                else:
                    # Validar capacidad
                    if hotel1.total_plazas and viaje.num_total_plazas > hotel1.total_plazas:
                        errors.append({
                            "rule": "HOTEL_CAPACITY_EXCEEDED",
                            "message": f"El hotel destino 1 {viaje.hotel_destino_1} que has solicitado no puede albergar tantos clientes",
                            "field": "num_total_plazas",
                            "details": {
                                "plazas_solicitadas": viaje.num_total_plazas,
                                "plazas_disponibles": hotel1.total_plazas
                            }
                        })

            # 2. Validar hotel destino 2 si existe
            if viaje.hotel_destino_2:
                result = await session.execute(
                    select(Hotel).where(Hotel.codigo == viaje.hotel_destino_2)
                )
                hotel2 = result.scalar_one_or_none()

                if not hotel2:
                    errors.append({
                        "rule": "HOTEL2_NOT_FOUND",
                        "message": f"Parece que el hotel destino 2 {viaje.hotel_destino_2} no está disponible en la lista de hoteles",
                        "field": "hotel_destino_2",
                        "value": viaje.hotel_destino_2
                    })
                else:
                    # Validar capacidad
                    if hotel2.total_plazas and viaje.num_total_plazas > hotel2.total_plazas:
                        errors.append({
                            "rule": "HOTEL2_CAPACITY_EXCEEDED",
                            "message": f"El hotel destino 2 {viaje.hotel_destino_2} que has solicitado no puede albergar tantos clientes",
                            "field": "num_total_plazas",
                            "details": {
                                "plazas_solicitadas": viaje.num_total_plazas,
                                "plazas_disponibles": hotel2.total_plazas
                            }
                        })

        return errors

    async def _validate_required_fields(self, viaje: Viaje) -> List[Dict[str, Any]]:
        """Validar campos obligatorios"""

        errors = []

        # Campos siempre obligatorios
        required_fields = {
            'num_reg': 'Número de registro',
            'temporada': 'Temporada',
            'tipo_turno': 'Tipo de turno',
            'zona_destino': 'Zona de destino',
            'provincia_hotel': 'Provincia del hotel',
            'dias_turno': 'Días de turno',
            'provincia_origen': 'Provincia de origen',
            'num_total_plazas': 'Número total de plazas',
            'hotel_destino_1': 'Hotel destino 1'
        }

        for field, description in required_fields.items():
            value = getattr(viaje, field, None)
            if not value:
                errors.append({
                    "rule": "REQUIRED_FIELD",
                    "message": f"El campo {description} es obligatorio",
                    "field": field
                })

        # Validaciones específicas de valores
        if viaje.tipo_turno and viaje.tipo_turno not in ['T', 'S']:
            errors.append({
                "rule": "INVALID_TIPO_TURNO",
                "message": "Tipo de turno debe ser 'T' o 'S'",
                "field": "tipo_turno",
                "value": viaje.tipo_turno
            })

        if viaje.viaje_combinado and viaje.viaje_combinado != 'N':
            errors.append({
                "rule": "INVALID_VIAJE_COMBINADO",
                "message": "Viaje combinado debe ser 'N'",
                "field": "viaje_combinado",
                "value": viaje.viaje_combinado
            })

        return errors

    async def _validate_batch_rules(self, session: AsyncSession, viajes: List[Viaje], lote: str) -> Dict[str, Any]:
        """Validar reglas a nivel de lote completo"""

        validations = {}

        try:
            # 1. Validar temporada única
            temporadas = set(v.temporada for v in viajes if v.temporada)
            if len(temporadas) > 1:
                validations['temporada_unica'] = {
                    "valid": False,
                    "message": f"Se han encontrado diferentes temporadas en la misma hoja: {list(temporadas)}",
                    "temporadas_encontradas": list(temporadas)
                }
            else:
                validations['temporada_unica'] = {"valid": True}

            # 2. Validar uso de todos los aeropuertos (solo si hay vuelos)
            aeropuertos_usados = set()
            for viaje in viajes:
                for aero in [viaje.aero_1_ida, viaje.aero_2_ida, viaje.aero_3_ida, viaje.aero_4_ida]:
                    if aero:
                        aeropuertos_usados.add(aero)

            if aeropuertos_usados:
                # Obtener todos los aeropuertos disponibles
                result = await session.execute(select(Aeropuerto.codigo_iata).where(Aeropuerto.activo == True))
                aeropuertos_disponibles = set(row[0] for row in result.fetchall())

                aeropuertos_faltantes = aeropuertos_disponibles - aeropuertos_usados

                if aeropuertos_faltantes:
                    validations['uso_aeropuertos'] = {
                        "valid": False,
                        "message": f"Faltan por listar los aeropuertos con código: {list(aeropuertos_faltantes)}",
                        "aeropuertos_faltantes": list(aeropuertos_faltantes)
                    }
                else:
                    validations['uso_aeropuertos'] = {"valid": True}

            # 3. Validar distribuciones de plazas
            distribuciones_validation = await self._validate_distributions_match(session, viajes, lote)
            validations['distribuciones'] = distribuciones_validation

        except Exception as e:
            logger.error(f"❌ Error en validaciones de lote: {e}")
            validations['error'] = str(e)

        return validations

    async def _validate_distributions_match(self, session: AsyncSession, viajes: List[Viaje], lote: str) -> Dict[str, Any]:
        """Validar que las plazas usadas coinciden con las establecidas"""

        try:
            # Agrupar viajes por clave de distribución
            used_distributions = {}

            for viaje in viajes:
                key = f"{viaje.provincia_origen}-{viaje.zona_destino}-{viaje.dias_turno}-{viaje.tipo_turno}"

                if key not in used_distributions:
                    used_distributions[key] = 0
                used_distributions[key] += viaje.num_total_plazas

            # Obtener distribuciones establecidas
            result = await session.execute(
                select(DistribucionPlaza).where(
                    and_(
                        DistribucionPlaza.lote == lote,
                        DistribucionPlaza.activo == True
                    )
                )
            )
            established_distributions = result.scalars().all()

            # Comparar
            mismatches = []
            for dist in established_distributions:
                key = f"{dist.provincia_origen}-{dist.zona_destino}-{dist.dias_turno}-{dist.tipo_turno}"
                used = used_distributions.get(key, 0)
                established = dist.plazas_establecidas

                if used != established:
                    mismatches.append({
                        "key": key,
                        "provincia_origen": dist.provincia_origen,
                        "zona_destino": dist.zona_destino,
                        "dias_turno": dist.dias_turno,
                        "tipo_turno": dist.tipo_turno,
                        "plazas_usadas": used,
                        "plazas_establecidas": established,
                        "diferencia": used - established
                    })

            if mismatches:
                return {
                    "valid": False,
                    "message": f"Encontradas {len(mismatches)} discrepancias en distribuciones de plazas",
                    "discrepancias": mismatches
                }
            else:
                return {"valid": True, "message": "Todas las distribuciones coinciden"}

        except Exception as e:
            logger.error(f"❌ Error validando distribuciones: {e}")
            return {"valid": False, "error": str(e)}

    async def _save_validation_results(self, session: AsyncSession, viaje: Viaje, errors: List[Dict[str, Any]]) -> None:
        """Guardar resultados de validación en la base de datos"""

        try:
            # Limpiar validaciones anteriores
            await session.execute(
                select(ValidacionViaje).where(ValidacionViaje.viaje_id == viaje.id)
            )

            # Guardar nuevas validaciones
            for error in errors:
                validacion = ValidacionViaje(
                    viaje_id=viaje.id,
                    regla_codigo=error.get("rule", "UNKNOWN"),
                    resultado="error",
                    mensaje=error.get("message", ""),
                    detalles=error
                )
                session.add(validacion)

            # Si no hay errores, crear registro de validación exitosa
            if not errors:
                validacion = ValidacionViaje(
                    viaje_id=viaje.id,
                    regla_codigo="ALL_RULES",
                    resultado="valido",
                    mensaje="Viaje válido - cumple todas las reglas",
                    detalles={}
                )
                session.add(validacion)

            # Actualizar estado del viaje
            viaje.estado = "validado" if not errors else "error"
            viaje.errores_validacion = {"total_errores": len(errors), "errores": errors}

        except Exception as e:
            logger.error(f"❌ Error guardando resultados de validación: {e}")

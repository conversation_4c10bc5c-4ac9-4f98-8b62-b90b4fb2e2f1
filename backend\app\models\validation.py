"""
Modelos de validación
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Text, ForeignKey, UUID
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.models.base import BaseModel, CodedModel


class ReglaValidacion(CodedModel):
    """Modelo de regla de validación"""
    
    __tablename__ = "reglas_validacion"
    
    nombre = Column(String(255), nullable=False)
    descripcion = Column(Text)
    tipo = Column(String(50), nullable=False)  # 'horario', 'transporte', 'hotel', 'distribucion'
    parametros = Column(JSONB)
    activa = Column(Boolean, default=True)
    
    # Relaciones
    validaciones = relationship("ValidacionViaje", back_populates="regla")


class ValidacionViaje(BaseModel):
    """Modelo de resultado de validación por viaje"""
    
    __tablename__ = "validaciones_viaje"
    
    viaje_id = Column(UUID, Foreign<PERSON>ey("viajes.id", ondelete="CASCADE"))
    regla_codigo = Column(String(50), ForeignKey("reglas_validacion.codigo"))
    resultado = Column(String(20), nullable=False)  # 'valido', 'error', 'advertencia'
    mensaje = Column(Text)
    detalles = Column(JSONB)
    
    # Relaciones
    viaje = relationship("Viaje", back_populates="validaciones")
    regla = relationship("ReglaValidacion", back_populates="validaciones")

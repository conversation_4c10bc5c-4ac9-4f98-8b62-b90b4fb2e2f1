"""
Modelos de transporte
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, String, ForeignKey, Date, DECIMAL, Time
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Vuelo(BaseModel):
    """Modelo de vuelo"""
    
    __tablename__ = "vuelos"
    
    codigo_vuelo = Column(String(20))
    aerolinea = Column(String(100))
    aeropuerto_origen = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    aeropuerto_destino = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    hora_salida = Column(Time)
    hora_llegada = Column(Time)
    dias_operacion = Column(String(20))  # L,M,X,J,V,S,D
    fecha_inicio_operacion = Column(Date)
    fecha_fin_operacion = Column(Date)
    precio = Column(DECIMAL(10, 2))
    plazas_disponibles = Column(Integer)
    activo = Column(Boolean, default=True)
    
    # Relaciones
    aeropuerto_origen_rel = relationship("Aeropuerto", foreign_keys=[aeropuerto_origen])
    aeropuerto_destino_rel = relationship("Aeropuerto", foreign_keys=[aeropuerto_destino])


class Tren(BaseModel):
    """Modelo de tren y AVE"""
    
    __tablename__ = "trenes"
    
    numero_tren = Column(String(20))
    tipo = Column(String(10))  # 'TREN', 'AVE'
    estacion_origen = Column(String(100))
    estacion_destino = Column(String(100))
    hora_salida = Column(Time)
    hora_llegada = Column(Time)
    dias_operacion = Column(String(20))
    fecha_inicio_operacion = Column(Date)
    fecha_fin_operacion = Column(Date)
    precio = Column(DECIMAL(10, 2))
    plazas_disponibles = Column(Integer)
    activo = Column(Boolean, default=True)


class RutaAutocar(BaseModel):
    """Modelo de ruta de autocar"""
    
    __tablename__ = "rutas_autocar"
    
    origen = Column(String(255), nullable=False)
    destino = Column(String(255), nullable=False)
    distancia_km = Column(Integer)
    tiempo_estimado_minutos = Column(Integer)
    precio_por_km = Column(DECIMAL(8, 4))
    activo = Column(Boolean, default=True)

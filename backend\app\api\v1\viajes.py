"""Endpoints para gestión de viajes"""
import uuid
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from loguru import logger
from app.core.database import get_db
from app.models.viaje import Viaje
from app.services.trip_generator import TripGenerator
from app.services.validation_engine import ValidationEngine

router = APIRouter()

@router.post("/generate")
async def generate_viajes(
    lote: str,
    temporada: str = "2025",
    background_tasks: BackgroundTasks = None,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Generar viajes automáticamente optimizando rutas y precios

    El sistema:
    1. Lee las distribuciones de plazas establecidas
    2. Encuentra hoteles disponibles en cada zona de destino
    3. Optimiza rutas minimizando distancia y coste
    4. Cumple todas las validaciones del IMSERSO
    5. Genera viajes en formato OUTCOME
    """

    # Validar lote
    if lote not in ['L1', 'L2', 'L3']:
        raise HTTPException(status_code=400, detail="Lote debe ser L1, L2 o L3")

    # Verificar si ya existen viajes para este lote
    existing_count = await db.execute(
        select(func.count(Viaje.id)).where(
            and_(Viaje.lote == lote, Viaje.temporada == temporada)
        )
    )
    existing = existing_count.scalar() or 0

    if existing > 0:
        return {
            "status": "confirmation_required",
            "message": f"Ya existen {existing} viajes para {lote}-{temporada}. ¿Desea regenerarlos?",
            "existing_count": existing,
            "lote": lote,
            "temporada": temporada,
            "options": ["regenerate", "cancel"]
        }

    # Generar ID de trabajo
    job_id = str(uuid.uuid4())

    # Iniciar generación en background
    if background_tasks:
        background_tasks.add_task(
            _generate_trips_background,
            job_id=job_id,
            lote=lote,
            temporada=temporada
        )

    return {
        "message": f"Generación de viajes para {lote}-{temporada} iniciada",
        "job_id": job_id,
        "lote": lote,
        "temporada": temporada,
        "status": "processing"
    }

@router.post("/generate/confirm")
async def confirm_generate_viajes(
    lote: str,
    temporada: str = "2025",
    action: str = Query(..., description="Acción: 'regenerate' o 'cancel'"),
    background_tasks: BackgroundTasks = None,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Confirmar regeneración de viajes existentes"""

    if action == "cancel":
        return {"message": "Generación cancelada", "status": "cancelled"}

    if action != "regenerate":
        raise HTTPException(status_code=400, detail="Acción debe ser 'regenerate' o 'cancel'")

    # Eliminar viajes existentes
    await db.execute(
        select(Viaje).where(and_(Viaje.lote == lote, Viaje.temporada == temporada))
    )
    await db.commit()

    # Generar nuevos viajes
    job_id = str(uuid.uuid4())

    if background_tasks:
        background_tasks.add_task(
            _generate_trips_background,
            job_id=job_id,
            lote=lote,
            temporada=temporada
        )

    return {
        "message": f"Regeneración de viajes para {lote}-{temporada} iniciada",
        "job_id": job_id,
        "lote": lote,
        "temporada": temporada,
        "status": "processing"
    }

@router.get("/generation/status/{job_id}")
async def get_generation_status(job_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Obtener estado de generación de viajes"""

    # TODO: Implementar seguimiento real del estado
    # Por ahora devolver estado simulado
    return {
        "job_id": job_id,
        "status": "completed",
        "progress": 100,
        "message": "Generación completada correctamente",
        "viajes_generados": 150,
        "viajes_validos": 148,
        "viajes_con_errores": 2
    }

@router.get("/")
async def list_viajes(
    lote: Optional[str] = Query(None, description="Filtrar por lote: L1, L2, L3"),
    temporada: Optional[str] = Query("2025", description="Filtrar por temporada"),
    estado: Optional[str] = Query(None, description="Filtrar por estado: generado, validado, error"),
    provincia_origen: Optional[str] = Query(None, description="Filtrar por provincia de origen"),
    zona_destino: Optional[str] = Query(None, description="Filtrar por zona de destino"),
    limit: int = Query(100, le=1000, description="Límite de resultados"),
    offset: int = Query(0, ge=0, description="Offset para paginación"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Listar viajes generados con filtros opcionales"""

    # Construir query base
    query = select(Viaje)
    count_query = select(func.count(Viaje.id))

    # Aplicar filtros
    filters = []

    if lote:
        filters.append(Viaje.lote == lote)
    if temporada:
        filters.append(Viaje.temporada == temporada)
    if estado:
        filters.append(Viaje.estado == estado)
    if provincia_origen:
        filters.append(Viaje.provincia_origen == provincia_origen)
    if zona_destino:
        filters.append(Viaje.zona_destino == zona_destino)

    if filters:
        query = query.where(and_(*filters))
        count_query = count_query.where(and_(*filters))

    # Aplicar paginación y ordenamiento
    query = query.order_by(Viaje.lote, Viaje.num_reg).offset(offset).limit(limit)

    # Ejecutar queries
    result = await db.execute(query)
    viajes = result.scalars().all()

    count_result = await db.execute(count_query)
    total = count_result.scalar() or 0

    return {
        "viajes": [
            {
                "id": str(viaje.id),
                "num_reg": viaje.num_reg,
                "lote": viaje.lote,
                "temporada": viaje.temporada,
                "tipo_turno": viaje.tipo_turno,
                "provincia_origen": viaje.provincia_origen,
                "zona_destino": viaje.zona_destino,
                "dias_turno": viaje.dias_turno,
                "num_total_plazas": viaje.num_total_plazas,
                "hotel_destino_1": viaje.hotel_destino_1,
                "estado": viaje.estado,
                "created_at": viaje.created_at.isoformat() if viaje.created_at else None,
                "tiene_errores": bool(viaje.errores_validacion)
            }
            for viaje in viajes
        ],
        "total": total,
        "limit": limit,
        "offset": offset,
        "filtros_aplicados": {
            "lote": lote,
            "temporada": temporada,
            "estado": estado,
            "provincia_origen": provincia_origen,
            "zona_destino": zona_destino
        }
    }

@router.get("/{viaje_id}")
async def get_viaje(viaje_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Obtener detalles completos de un viaje"""

    try:
        viaje_uuid = uuid.UUID(viaje_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de viaje inválido")

    result = await db.execute(
        select(Viaje).where(Viaje.id == viaje_uuid)
    )
    viaje = result.scalar_one_or_none()

    if not viaje:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    return {
        "id": str(viaje.id),
        "num_reg": viaje.num_reg,
        "lote": viaje.lote,
        "temporada": viaje.temporada,
        "tipo_turno": viaje.tipo_turno,
        "zona_destino": viaje.zona_destino,
        "provincia_hotel": viaje.provincia_hotel,
        "dias_turno": viaje.dias_turno,
        "provincia_origen": viaje.provincia_origen,
        "num_total_plazas": viaje.num_total_plazas,

        # Hoteles
        "hotel_destino_1": viaje.hotel_destino_1,
        "hotel_destino_2": viaje.hotel_destino_2,

        # Transportes
        "transportes_ida": {
            "trans_1": viaje.trans_1_ida,
            "trans_2": viaje.trans_2_ida,
            "trans_3": viaje.trans_3_ida,
            "trans_4": viaje.trans_4_ida
        },
        "transportes_vuelta": {
            "trans_1": viaje.trans_1_vuelta,
            "trans_2": viaje.trans_2_vuelta,
            "trans_3": viaje.trans_3_vuelta,
            "trans_4": viaje.trans_4_vuelta
        },

        # Aeropuertos
        "aeropuertos_ida": {
            "aero_1": viaje.aero_1_ida,
            "aero_2": viaje.aero_2_ida,
            "aero_3": viaje.aero_3_ida,
            "aero_4": viaje.aero_4_ida
        },

        # Horarios
        "horarios_ida": {
            "hora_inicio": viaje.hora_inicio_ida.strftime('%H:%M') if viaje.hora_inicio_ida else None,
            "hora_inicio_2": viaje.hora_inicio_2_ida.strftime('%H:%M') if viaje.hora_inicio_2_ida else None,
            "hora_inicio_3": viaje.hora_inicio_3_ida.strftime('%H:%M') if viaje.hora_inicio_3_ida else None,
            "hora_inicio_4": viaje.hora_inicio_4_ida.strftime('%H:%M') if viaje.hora_inicio_4_ida else None,
            "hora_fin": viaje.hora_fin_ida.strftime('%H:%M') if viaje.hora_fin_ida else None
        },
        "horarios_vuelta": {
            "hora_inicio": viaje.hora_inicio_vuelta.strftime('%H:%M') if viaje.hora_inicio_vuelta else None,
            "hora_inicio_2": viaje.hora_inicio_2_vuelta.strftime('%H:%M') if viaje.hora_inicio_2_vuelta else None,
            "hora_inicio_3": viaje.hora_inicio_3_vuelta.strftime('%H:%M') if viaje.hora_inicio_3_vuelta else None,
            "hora_inicio_4": viaje.hora_inicio_4_vuelta.strftime('%H:%M') if viaje.hora_inicio_4_vuelta else None,
            "hora_fin": viaje.hora_fin_vuelta.strftime('%H:%M') if viaje.hora_fin_vuelta else None
        },

        # Kilómetros y servicios
        "total_km_ida": viaje.total_km_ida,
        "total_km_vuelta": viaje.total_km_vuelta,
        "serv_1_ruta_ida": viaje.serv_1_ruta_ida,
        "serv_1_ruta_vuelta": viaje.serv_1_ruta_vuelta,

        # Estado y validaciones
        "viaje_combinado": viaje.viaje_combinado,
        "estado": viaje.estado,
        "errores_validacion": viaje.errores_validacion,
        "observaciones": viaje.observaciones,

        # Auditoría
        "created_at": viaje.created_at.isoformat() if viaje.created_at else None,
        "updated_at": viaje.updated_at.isoformat() if viaje.updated_at else None
    }

@router.delete("/{viaje_id}")
async def delete_viaje(viaje_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Eliminar un viaje específico"""

    try:
        viaje_uuid = uuid.UUID(viaje_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de viaje inválido")

    result = await db.execute(
        select(Viaje).where(Viaje.id == viaje_uuid)
    )
    viaje = result.scalar_one_or_none()

    if not viaje:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    await db.delete(viaje)
    await db.commit()

    return {"message": f"Viaje {viaje.num_reg} eliminado correctamente"}

@router.delete("/batch")
async def delete_viajes_batch(
    lote: str,
    temporada: str = "2025",
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Eliminar todos los viajes de un lote específico"""

    if lote not in ['L1', 'L2', 'L3']:
        raise HTTPException(status_code=400, detail="Lote debe ser L1, L2 o L3")

    # Contar viajes a eliminar
    count_result = await db.execute(
        select(func.count(Viaje.id)).where(
            and_(Viaje.lote == lote, Viaje.temporada == temporada)
        )
    )
    count = count_result.scalar() or 0

    if count == 0:
        raise HTTPException(status_code=404, detail=f"No se encontraron viajes para {lote}-{temporada}")

    # Eliminar viajes
    await db.execute(
        select(Viaje).where(and_(Viaje.lote == lote, Viaje.temporada == temporada))
    )
    await db.commit()

    return {
        "message": f"Eliminados {count} viajes de {lote}-{temporada}",
        "viajes_eliminados": count,
        "lote": lote,
        "temporada": temporada
    }


async def _generate_trips_background(job_id: str, lote: str, temporada: str) -> None:
    """Función para generar viajes en background"""

    try:
        logger.info(f"🚀 Iniciando generación background {job_id}: {lote}-{temporada}")

        # Crear generador de viajes
        trip_generator = TripGenerator()

        # Generar viajes optimizados
        optimized_trips = await trip_generator.generate_trips_for_lote(lote, temporada)

        # Guardar en base de datos
        saved_ids = await trip_generator.save_generated_trips(optimized_trips)

        # Validar viajes generados
        validation_engine = ValidationEngine()
        validation_results = await validation_engine.validate_batch(lote, temporada)

        logger.info(f"✅ Generación completada {job_id}: {len(saved_ids)} viajes, {validation_results['viajes_validos']} válidos")

    except Exception as e:
        logger.error(f"❌ Error en generación background {job_id}: {e}")
        raise

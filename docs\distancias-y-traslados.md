# Sistema de Distancias Reales y Traslados

## 🎯 Objetivo

Calcular distancias reales por carretera usando APIs de mapas gratuitas e incluir automáticamente los traslados aeropuerto/estación ↔ hotel en los viajes generados.

## 🗺️ Cálculo de Distancias Reales

### **Problema Anterior**
- Distancias aproximadas usando fórmula de Haversine
- No consideraba rutas reales por carretera
- Ejemplo: Madrid → Benidorm = 350km (línea recta) vs 460km (carretera real)

### **Solución Implementada**
- **APIs gratuitas** para distancias reales
- **Fallback inteligente** si las APIs fallan
- **Cache** para evitar llamadas repetidas

### **APIs Utilizadas (Gratuitas)**

#### 1. **OpenRouteService** (Recomendado)
```bash
# Registro: https://openrouteservice.org/dev/#/signup
# Límite: 2000 requests/día GRATIS
# Precisión: Excelente (datos OpenStreetMap)

OPENROUTE_API_KEY="tu-api-key-aqui"
```

#### 2. **GraphHopper** (Alternativo)
```bash
# Registro: https://www.graphhopper.com/
# Límite: 2500 requests/día GRATIS
# Precisión: Muy buena

GRAPHHOPPER_API_KEY="tu-api-key-aqui"
```

#### 3. **Fallback Haversine** (Sin API)
```bash
# Si fallan las APIs, usa cálculo aproximado
# Factor de corrección: +30% para carreteras
# Siempre funciona, menos preciso
```

### **Ejemplo de Uso**

```python
# Calcular distancia Madrid → Benidorm
distance_result = await distance_calculator.calculate_distance(
    origen="28",      # Madrid
    destino="03",     # Alicante (Benidorm)
    transport_type="driving"
)

# Resultado:
# distance_km: 460
# duration_minutes: 345  # ~5h 45min
# api_used: "openroute"
# success: True
```

## 🚌 Sistema de Traslados

### **¿Qué son los Traslados?**
Transportes desde aeropuerto/estación ferroviaria hasta el hotel y viceversa.

### **Cuándo se Añaden Automáticamente**
- **Vuelos**: Siempre (aeropuerto → hotel)
- **Trenes/AVE**: Siempre (estación → hotel)
- **Autocares**: NO (van directamente al hotel)

### **Tipos de Traslado Disponibles**

#### 1. **Autocar** (Preferido para grupos)
```python
# Características:
precio_por_km = 0.08  # €0.08/km
velocidad = 40        # 40 km/h en ciudad
precio_minimo = 8.0   # €8 mínimo
```

#### 2. **Taxi** (Más caro, más directo)
```python
# Características:
precio_por_km = 1.20  # €1.20/km
velocidad = 35        # 35 km/h en ciudad
precio_minimo = 15.0  # €15 mínimo + €3 bajada bandera
```

#### 3. **Transporte Público** (Muy económico)
```python
# Disponible en:
ciudades_con_metro = {
    '28': ['metro', 'bus'],      # Madrid
    '08': ['metro', 'bus'],      # Barcelona
    '46': ['metro', 'bus'],      # Valencia
    '48': ['metro', 'bus'],      # Bilbao
    '41': ['metro', 'bus'],      # Sevilla
}

precio_por_km = 0.05  # €0.05/km (muy económico)
```

### **Cálculo Automático de Traslados**

```python
# Ejemplo: Vuelo Madrid → Málaga
# 1. Vuelo: MAD → AGP (1h 30min)
# 2. Traslado automático: AGP → Hotel Málaga

transfer_options = await transfer_service.calculate_transfers(
    aeropuerto_o_estacion="AGP",
    hotel_coords=(36.7213, -4.4214),  # Málaga centro
    provincia_hotel="29"
)

# Opciones generadas:
# - Autocar: 25km, 40min, €12.00
# - Taxi: 25km, 35min, €33.00  
# - Bus: 25km, 50min, €3.50
```

### **Selección Automática**
El sistema selecciona automáticamente la opción **más económica** para grupos:
1. **Autocar** (preferido)
2. **Transporte público** (si disponible)
3. **Taxi** (último recurso)

## 🔄 Integración en Rutas

### **Antes (Sin Traslados)**
```
Madrid → Málaga:
1. Vuelo MAD → AGP (1h 30min, 460km aéreo)
```

### **Después (Con Traslados)**
```
Madrid → Málaga:
1. Vuelo MAD → AGP (1h 30min, 460km aéreo)
2. Traslado AGP → Hotel (40min, 25km carretera, €12)

Total: 2h 10min, 485km, precio optimizado
```

## 📊 Impacto en la Optimización

### **Distancias Más Precisas**
| Ruta | Haversine | API Real | Diferencia |
|------|-----------|----------|------------|
| Madrid → Benidorm | 350km | 460km | +31% |
| Barcelona → Sevilla | 830km | 1030km | +24% |
| Bilbao → Valencia | 520km | 650km | +25% |

### **Costes Más Realistas**
```python
# Antes (aproximado):
madrid_benidorm = 350 * 0.12 = €42.00

# Después (real):
madrid_benidorm = 460 * 0.12 = €55.20
traslado_aeropuerto = €12.00
total = €67.20  # +60% más realista
```

## 🚀 Configuración y Uso

### **1. Obtener API Keys (Gratuitas)**

#### OpenRouteService:
1. Ir a https://openrouteservice.org/dev/#/signup
2. Crear cuenta gratuita
3. Obtener API key
4. Añadir a `.env`: `OPENROUTE_API_KEY="tu-key"`

#### GraphHopper:
1. Ir a https://www.graphhopper.com/
2. Crear cuenta gratuita  
3. Obtener API key
4. Añadir a `.env`: `GRAPHHOPPER_API_KEY="tu-key"`

### **2. Configurar Variables de Entorno**
```bash
# Copiar archivo de ejemplo
cp backend/.env.example backend/.env

# Editar con tus API keys
nano backend/.env
```

### **3. Verificar Funcionamiento**
```bash
# Levantar sistema
docker-compose up -d

# Generar viajes (usará distancias reales)
curl -X POST "http://localhost:8000/api/v1/viajes/generate" \
  -d '{"lote": "L1"}'

# Los logs mostrarán:
# 🗺️ Distancia calculada: 28 → 29 = 460km (openroute)
# 🚌 Traslado añadido: autocar (25km, €12.00)
```

## 📈 Beneficios del Sistema

### **1. Precisión Mejorada**
- **Distancias reales** por carretera
- **Tiempos de viaje** precisos
- **Costes realistas** de transporte

### **2. Traslados Automáticos**
- **Sin intervención manual** necesaria
- **Optimización automática** del medio de transporte
- **Cálculo de horarios** completo

### **3. Robustez**
- **Múltiples APIs** como respaldo
- **Fallback inteligente** si fallan las APIs
- **Cache** para evitar llamadas repetidas

### **4. Coste Cero**
- **APIs gratuitas** con límites generosos
- **2000-2500 requests/día** suficientes para uso normal
- **Fallback sin coste** si se agotan los límites

## 🔮 Funcionalidades Futuras

### **Traslados Avanzados**
- **Múltiples opciones** por traslado
- **Horarios de transporte público** reales
- **Reserva automática** de traslados

### **Optimización Dinámica**
- **Tráfico en tiempo real**
- **Rutas alternativas** según condiciones
- **Precios dinámicos** según demanda

### **Integración Completa**
- **APIs de aerolíneas** para horarios reales
- **RENFE API** para trenes
- **Booking APIs** para hoteles con coordenadas exactas

---

**El sistema transforma el cálculo de distancias de aproximado a preciso, y añade automáticamente todos los traslados necesarios, resultando en viajes completamente optimizados y realistas.**

"""Endpoints para gestión de transportes"""
from typing import Any
from fastapi import APIRouter, Depends, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db

router = APIRouter()

@router.post("/upload")
async def upload_transportes(file: UploadFile = File(...), db: AsyncSession = Depends(get_db)) -> Any:
    """Subir archivo de transportes (vuelos, trenes, AVE)"""
    return {"message": f"Archivo {file.filename} procesado correctamente", "transportes_importados": 0}

@router.get("/vuelos")
async def list_vuelos(db: AsyncSession = Depends(get_db)) -> Any:
    """Listar vuelos"""
    return {"vuelos": []}

@router.get("/trenes")
async def list_trenes(db: AsyncSession = Depends(get_db)) -> Any:
    """Listar trenes y AVE"""
    return {"trenes": []}

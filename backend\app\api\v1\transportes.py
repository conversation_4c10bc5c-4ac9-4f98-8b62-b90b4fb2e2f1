"""Endpoints para gestión de transportes"""
import os
import tempfile
from typing import Any, Optional
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from app.core.database import get_db
from app.services.data_import_service import DataImportService
from app.models.transport import Vuelo, Tren

router = APIRouter()

@router.post("/upload/vuelos")
async def upload_vuelos(
    file: UploadFile = File(...),
    action: Optional[str] = Query(None, description="Acción: 'overwrite', 'append', o None para preguntar"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Subir archivo de vuelos"""

    if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
        raise HTTPException(status_code=400, detail="Formato de archivo no soportado")

    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
        content = await file.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name

    try:
        import_service = DataImportService()
        result = await import_service.handle_file_import(
            file_path=tmp_file_path,
            import_type='vuelos',
            action=action or 'ask'
        )

        return {
            "filename": file.filename,
            "size": len(content),
            **result
        }

    finally:
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)

@router.post("/upload/trenes")
async def upload_trenes(
    file: UploadFile = File(...),
    action: Optional[str] = Query(None, description="Acción: 'overwrite', 'append', o None para preguntar"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Subir archivo de trenes y AVE"""

    if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
        raise HTTPException(status_code=400, detail="Formato de archivo no soportado")

    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
        content = await file.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name

    try:
        import_service = DataImportService()
        result = await import_service.handle_file_import(
            file_path=tmp_file_path,
            import_type='trenes',
            action=action or 'ask'
        )

        return {
            "filename": file.filename,
            "size": len(content),
            **result
        }

    finally:
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)

@router.get("/vuelos")
async def list_vuelos(
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    origen: Optional[str] = Query(None),
    destino: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Listar vuelos con filtros opcionales"""

    query = select(Vuelo).where(Vuelo.activo == True)

    if origen:
        query = query.where(Vuelo.aeropuerto_origen == origen)
    if destino:
        query = query.where(Vuelo.aeropuerto_destino == destino)

    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    vuelos = result.scalars().all()

    # Contar total
    count_query = select(func.count(Vuelo.id)).where(Vuelo.activo == True)
    if origen:
        count_query = count_query.where(Vuelo.aeropuerto_origen == origen)
    if destino:
        count_query = count_query.where(Vuelo.aeropuerto_destino == destino)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    return {
        "vuelos": [
            {
                "id": str(vuelo.id),
                "codigo_vuelo": vuelo.codigo_vuelo,
                "aerolinea": vuelo.aerolinea,
                "aeropuerto_origen": vuelo.aeropuerto_origen,
                "aeropuerto_destino": vuelo.aeropuerto_destino,
                "hora_salida": str(vuelo.hora_salida) if vuelo.hora_salida else None,
                "hora_llegada": str(vuelo.hora_llegada) if vuelo.hora_llegada else None,
                "precio": float(vuelo.precio) if vuelo.precio else None,
                "plazas_disponibles": vuelo.plazas_disponibles,
            }
            for vuelo in vuelos
        ],
        "total": total,
        "limit": limit,
        "offset": offset
    }

@router.get("/trenes")
async def list_trenes(
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    tipo: Optional[str] = Query(None, description="Tipo: 'TREN' o 'AVE'"),
    origen: Optional[str] = Query(None),
    destino: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Listar trenes y AVE con filtros opcionales"""

    query = select(Tren).where(Tren.activo == True)

    if tipo:
        query = query.where(Tren.tipo == tipo)
    if origen:
        query = query.where(Tren.estacion_origen.ilike(f"%{origen}%"))
    if destino:
        query = query.where(Tren.estacion_destino.ilike(f"%{destino}%"))

    query = query.offset(offset).limit(limit)

    result = await db.execute(query)
    trenes = result.scalars().all()

    # Contar total
    count_query = select(func.count(Tren.id)).where(Tren.activo == True)
    if tipo:
        count_query = count_query.where(Tren.tipo == tipo)
    if origen:
        count_query = count_query.where(Tren.estacion_origen.ilike(f"%{origen}%"))
    if destino:
        count_query = count_query.where(Tren.estacion_destino.ilike(f"%{destino}%"))

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    return {
        "trenes": [
            {
                "id": str(tren.id),
                "numero_tren": tren.numero_tren,
                "tipo": tren.tipo,
                "estacion_origen": tren.estacion_origen,
                "estacion_destino": tren.estacion_destino,
                "hora_salida": str(tren.hora_salida) if tren.hora_salida else None,
                "hora_llegada": str(tren.hora_llegada) if tren.hora_llegada else None,
                "precio": float(tren.precio) if tren.precio else None,
                "plazas_disponibles": tren.plazas_disponibles,
            }
            for tren in trenes
        ],
        "total": total,
        "limit": limit,
        "offset": offset
    }

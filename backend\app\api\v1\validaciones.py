"""Endpoints para validaciones"""
import uuid
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from app.core.database import get_db
from app.models.validation import ReglaValidacion, ValidacionViaje
from app.models.viaje import Viaje
from app.services.validation_engine import ValidationEngine

router = APIRouter()

@router.post("/validate-batch")
async def validate_batch(
    lote: str,
    temporada: str = "2025",
    background_tasks: BackgroundTasks = None,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Validar lote completo de viajes contra todas las reglas IMSERSO

    Ejecuta todas las validaciones:
    - Reglas de transporte (primer/último autocar, límites km)
    - Reglas de horarios (mínimos/máximos, secuencias)
    - Reglas de servicios (obligatorios según horarios)
    - Reglas de hoteles (capacidad, disponibilidad)
    - Reglas de distribución (plazas establecidas vs usadas)
    """

    if lote not in ['L1', 'L2', 'L3']:
        raise HTTPException(status_code=400, detail="Lote debe ser L1, L2 o L3")

    # Verificar que existen viajes para validar
    count_result = await db.execute(
        select(func.count(Viaje.id)).where(
            and_(Viaje.lote == lote, Viaje.temporada == temporada)
        )
    )
    viajes_count = count_result.scalar() or 0

    if viajes_count == 0:
        raise HTTPException(
            status_code=404,
            detail=f"No se encontraron viajes para validar en {lote}-{temporada}"
        )

    # Generar ID de trabajo
    job_id = str(uuid.uuid4())

    # Iniciar validación en background
    if background_tasks:
        background_tasks.add_task(
            _validate_batch_background,
            job_id=job_id,
            lote=lote,
            temporada=temporada
        )

    return {
        "message": f"Validación de {lote}-{temporada} iniciada",
        "job_id": job_id,
        "lote": lote,
        "temporada": temporada,
        "viajes_a_validar": viajes_count,
        "status": "processing"
    }

@router.get("/batch-status/{job_id}")
async def get_validation_status(job_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Obtener estado de validación de lote"""

    # TODO: Implementar seguimiento real del estado
    return {
        "job_id": job_id,
        "status": "completed",
        "progress": 100,
        "message": "Validación completada",
        "total_viajes": 150,
        "viajes_validos": 145,
        "viajes_con_errores": 5,
        "errores_por_regla": {
            "FIRST_TRANSPORT_BUS": 2,
            "SERVICE_REQUIRED_IDA": 3
        }
    }

@router.get("/batch-results/{lote}")
async def get_batch_validation_results(
    lote: str,
    temporada: str = "2025",
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Obtener resultados completos de validación de un lote"""

    if lote not in ['L1', 'L2', 'L3']:
        raise HTTPException(status_code=400, detail="Lote debe ser L1, L2 o L3")

    # Ejecutar validación directamente
    validation_engine = ValidationEngine()
    results = await validation_engine.validate_batch(lote, temporada)

    return {
        "lote": lote,
        "temporada": temporada,
        "resultados": results,
        "timestamp": "2025-01-07T00:00:00Z"
    }

@router.get("/rules")
async def list_validation_rules(
    tipo: Optional[str] = Query(None, description="Filtrar por tipo: transporte, horario, servicio, hotel, distribucion"),
    activa: Optional[bool] = Query(None, description="Filtrar por estado activo"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Listar reglas de validación configuradas"""

    query = select(ReglaValidacion)

    if tipo:
        query = query.where(ReglaValidacion.tipo == tipo)
    if activa is not None:
        query = query.where(ReglaValidacion.activa == activa)

    query = query.order_by(ReglaValidacion.tipo, ReglaValidacion.codigo)

    result = await db.execute(query)
    reglas = result.scalars().all()

    return {
        "reglas": [
            {
                "codigo": regla.codigo,
                "nombre": regla.nombre,
                "descripcion": regla.descripcion,
                "tipo": regla.tipo,
                "parametros": regla.parametros,
                "activa": regla.activa,
                "created_at": regla.created_at.isoformat() if regla.created_at else None
            }
            for regla in reglas
        ],
        "total": len(reglas),
        "filtros": {
            "tipo": tipo,
            "activa": activa
        }
    }

@router.get("/results/{viaje_id}")
async def get_validation_results(viaje_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Obtener resultados de validación de un viaje específico"""

    try:
        viaje_uuid = uuid.UUID(viaje_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de viaje inválido")

    # Verificar que el viaje existe
    viaje_result = await db.execute(
        select(Viaje).where(Viaje.id == viaje_uuid)
    )
    viaje = viaje_result.scalar_one_or_none()

    if not viaje:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Obtener validaciones del viaje
    validaciones_result = await db.execute(
        select(ValidacionViaje).where(ValidacionViaje.viaje_id == viaje_uuid)
        .order_by(ValidacionViaje.created_at)
    )
    validaciones = validaciones_result.scalars().all()

    # Agrupar por resultado
    errores = [v for v in validaciones if v.resultado == "error"]
    advertencias = [v for v in validaciones if v.resultado == "advertencia"]
    validos = [v for v in validaciones if v.resultado == "valido"]

    return {
        "viaje_id": viaje_id,
        "num_reg": viaje.num_reg,
        "lote": viaje.lote,
        "estado_general": viaje.estado,
        "es_valido": len(errores) == 0,
        "resumen": {
            "total_validaciones": len(validaciones),
            "errores": len(errores),
            "advertencias": len(advertencias),
            "validos": len(validos)
        },
        "validaciones": [
            {
                "regla_codigo": val.regla_codigo,
                "resultado": val.resultado,
                "mensaje": val.mensaje,
                "detalles": val.detalles,
                "validado_at": val.created_at.isoformat() if val.created_at else None
            }
            for val in validaciones
        ],
        "errores_criticos": [
            {
                "regla": error.regla_codigo,
                "mensaje": error.mensaje,
                "detalles": error.detalles
            }
            for error in errores
        ]
    }

@router.post("/validate-single/{viaje_id}")
async def validate_single_trip(viaje_id: str, db: AsyncSession = Depends(get_db)) -> Any:
    """Validar un viaje individual"""

    try:
        viaje_uuid = uuid.UUID(viaje_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de viaje inválido")

    # Obtener el viaje
    viaje_result = await db.execute(
        select(Viaje).where(Viaje.id == viaje_uuid)
    )
    viaje = viaje_result.scalar_one_or_none()

    if not viaje:
        raise HTTPException(status_code=404, detail="Viaje no encontrado")

    # Validar el viaje
    validation_engine = ValidationEngine()
    is_valid, errors = await validation_engine.validate_trip(viaje)

    return {
        "viaje_id": viaje_id,
        "num_reg": viaje.num_reg,
        "es_valido": is_valid,
        "total_errores": len(errors),
        "errores": errors,
        "mensaje": "Viaje válido" if is_valid else f"Viaje con {len(errors)} errores",
        "timestamp": "2025-01-07T00:00:00Z"
    }

@router.put("/rules/{regla_codigo}")
async def update_validation_rule(
    regla_codigo: str,
    activa: bool,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """Activar/desactivar una regla de validación"""

    result = await db.execute(
        select(ReglaValidacion).where(ReglaValidacion.codigo == regla_codigo)
    )
    regla = result.scalar_one_or_none()

    if not regla:
        raise HTTPException(status_code=404, detail=f"Regla {regla_codigo} no encontrada")

    regla.activa = activa
    await db.commit()

    return {
        "regla_codigo": regla_codigo,
        "nombre": regla.nombre,
        "activa": regla.activa,
        "mensaje": f"Regla {'activada' if activa else 'desactivada'} correctamente"
    }


async def _validate_batch_background(job_id: str, lote: str, temporada: str) -> None:
    """Función para validar lote en background"""

    try:
        # Crear motor de validaciones
        validation_engine = ValidationEngine()

        # Ejecutar validación completa
        results = await validation_engine.validate_batch(lote, temporada)

        # TODO: Guardar resultados del job para consulta posterior

    except Exception as e:
        # TODO: Guardar error del job
        raise

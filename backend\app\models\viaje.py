"""
Modelo de viajes
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, ForeignKey, Time, Text, UUID
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Viaje(BaseModel):
    """Modelo de viaje generado (equivalente a L1-OUTCOME, L2-OUTCOME, L3-OUTCOME)"""
    
    __tablename__ = "viajes"
    
    num_reg = Column(Integer, nullable=False, index=True)
    lote = Column(String(10), nullable=False, index=True)  # L1, L2, L3
    temporada = Column(String(10), nullable=False)
    tipo_turno = Column(String(1), nullable=False)  # T, S
    zona_destino = Column(String(10), ForeignKey("zonas_destino.codigo"))
    provincia_hotel = Column(String(10), ForeignKey("provincias.codigo"))
    dias_turno = Column(Integer, nullable=False)
    provincia_origen = Column(String(10), ForeignKey("provincias.codigo"))
    num_total_plazas = Column(Integer, nullable=False)
    
    # Hoteles
    hotel_destino_1 = Column(String(20), ForeignKey("hoteles.codigo"))
    hotel_destino_2 = Column(String(20), ForeignKey("hoteles.codigo"))
    
    # Transportes IDA
    trans_1_ida = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_2_ida = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_3_ida = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_4_ida = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    
    # Transportes VUELTA
    trans_1_vuelta = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_2_vuelta = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_3_vuelta = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    trans_4_vuelta = Column(String(1), ForeignKey("tipos_transporte.codigo"))
    
    # Aeropuertos
    aero_1_ida = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    aero_2_ida = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    aero_3_ida = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    aero_4_ida = Column(String(3), ForeignKey("aeropuertos.codigo_iata"))
    
    # Horarios IDA
    hora_inicio_ida = Column(Time)
    hora_inicio_2_ida = Column(Time)
    hora_inicio_3_ida = Column(Time)
    hora_inicio_4_ida = Column(Time)
    hora_fin_ida = Column(Time)
    
    # Horarios VUELTA
    hora_inicio_vuelta = Column(Time)
    hora_inicio_2_vuelta = Column(Time)
    hora_inicio_3_vuelta = Column(Time)
    hora_inicio_4_vuelta = Column(Time)
    hora_fin_vuelta = Column(Time)
    
    # Kilómetros
    total_km_ida = Column(Integer)
    total_km_vuelta = Column(Integer)
    
    # Servicios en ruta
    serv_1_ruta_ida = Column(String(10), ForeignKey("tipos_servicio.codigo"))
    serv_1_ruta_vuelta = Column(String(10), ForeignKey("tipos_servicio.codigo"))
    
    # Campos adicionales
    viaje_combinado = Column(String(1), default='N')
    observaciones = Column(Text)
    
    # Control de estado
    estado = Column(String(20), default='generado')  # generado, validado, exportado, error
    errores_validacion = Column(JSONB)
    
    # Auditoría
    created_by = Column(UUID, ForeignKey("users.id"))
    
    # Relaciones
    zona_destino_rel = relationship("ZonaDestino", back_populates="viajes")
    provincia_hotel_rel = relationship("Provincia", foreign_keys=[provincia_hotel])
    provincia_origen_rel = relationship("Provincia", foreign_keys=[provincia_origen])
    hotel_destino_1_rel = relationship("Hotel", foreign_keys=[hotel_destino_1])
    hotel_destino_2_rel = relationship("Hotel", foreign_keys=[hotel_destino_2])
    created_by_user = relationship("User", back_populates="viajes_creados")
    validaciones = relationship("ValidacionViaje", back_populates="viaje")

"""
Servicio para calcular distancias reales usando APIs de mapas gratuitas
"""

import asyncio
import httpx
import json
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass
from loguru import logger

from app.core.config import settings


@dataclass
class DistanceResult:
    """Resultado de cálculo de distancia"""
    distance_km: int
    duration_minutes: int
    route_type: str  # 'driving', 'transit', 'walking'
    api_used: str
    success: bool
    error_message: Optional[str] = None


class DistanceCalculator:
    """Calculadora de distancias reales usando APIs gratuitas"""
    
    def __init__(self):
        # Cache de distancias calculadas para evitar llamadas repetidas
        self._distance_cache: Dict[str, DistanceResult] = {}
        
        # Coordenadas de capitales de provincia españolas
        self.provincia_coords = {
            '01': (42.8467, -2.6716),   # Álava - Vitoria
            '02': (38.9942, -1.8564),   # Albacete
            '03': (38.3452, -0.4810),   # Alicante
            '04': (36.8381, -2.4597),   # Almería
            '05': (40.6566, -4.7016),   # <PERSON><PERSON>
            '06': (38.8794, -6.9707),   # Badajoz
            '07': (39.5696, 2.6502),    # Baleares - <PERSON>
            '08': (41.3851, 2.1734),    # Barcelona
            '09': (42.3440, -3.6969),   # Burgos
            '10': (39.4753, -6.3724),   # Cáceres
            '11': (36.5270, -6.2885),   # Cádiz
            '12': (39.9864, -0.0513),   # Castellón
            '13': (38.9848, -3.9254),   # Ciudad Real
            '14': (37.8882, -4.7794),   # Córdoba
            '15': (43.3623, -8.4115),   # A Coruña
            '16': (40.0703, -2.1374),   # Cuenca
            '17': (41.9794, 2.8214),    # Girona
            '18': (37.1773, -3.5986),   # Granada
            '19': (40.6320, -3.1601),   # Guadalajara
            '20': (43.3183, -1.9812),   # Guipúzcoa - San Sebastián
            '21': (37.2614, -6.9447),   # Huelva
            '22': (42.1401, -0.4086),   # Huesca
            '23': (37.7796, -3.7849),   # Jaén
            '24': (42.5987, -5.5671),   # León
            '25': (41.6176, 0.6200),    # Lleida
            '26': (42.2871, -2.5396),   # La Rioja - Logroño
            '27': (43.0097, -7.5567),   # Lugo
            '28': (40.4168, -3.7038),   # Madrid
            '29': (36.7213, -4.4214),   # Málaga
            '30': (37.9922, -1.1307),   # Murcia
            '31': (42.8169, -1.6432),   # Navarra - Pamplona
            '32': (42.3370, -7.8640),   # Ourense
            '33': (43.3614, -5.8593),   # Asturias - Oviedo
            '34': (42.0096, -4.5284),   # Palencia
            '35': (28.1248, -15.4300),  # Las Palmas
            '36': (42.4296, -8.6446),   # Pontevedra
            '37': (40.9701, -5.6635),   # Salamanca
            '38': (28.4636, -16.2518),  # Santa Cruz de Tenerife
            '39': (43.4623, -3.8099),   # Cantabria - Santander
            '40': (40.9429, -4.1088),   # Segovia
            '41': (37.3891, -5.9845),   # Sevilla
            '42': (41.7665, -2.9516),   # Soria
            '43': (41.1189, 1.2445),    # Tarragona
            '44': (40.3456, -1.1063),   # Teruel
            '45': (39.8628, -4.0273),   # Toledo
            '46': (39.4699, -0.3763),   # Valencia
            '47': (41.6523, -4.7245),   # Valladolid
            '48': (43.2627, -2.9253),   # Vizcaya - Bilbao
            '49': (41.5034, -5.7447),   # Zamora
            '50': (41.6488, -0.8891),   # Zaragoza
            '51': (35.8894, -5.3213),   # Ceuta
            '52': (35.2919, -2.9381),   # Melilla
        }
        
        # Coordenadas de aeropuertos principales
        self.airport_coords = {
            'MAD': (40.4719, -3.5626),  # Madrid-Barajas
            'BCN': (41.2971, 2.0785),   # Barcelona-El Prat
            'SVQ': (37.4180, -5.8931),  # Sevilla
            'AGP': (36.6749, -4.4991),  # Málaga-Costa del Sol
            'VLC': (39.4893, -0.4816),  # Valencia
            'ALC': (38.2822, -0.5581),  # Alicante-Elche
            'BIO': (43.3011, -2.9106),  # Bilbao
            'PMI': (39.5517, 2.7388),   # Palma de Mallorca
            'LPA': (27.9319, -15.3866), # Las Palmas
            'TFS': (28.0445, -16.5725), # Tenerife Sur
            'SCQ': (42.8963, -8.4151),  # Santiago de Compostela
            'OVD': (43.5636, -6.0346),  # Asturias
            'SDR': (43.4270, -3.8200),  # Santander
            'VIT': (42.8828, -2.7244),  # Vitoria
            'ZAZ': (41.6661, -1.0417),  # Zaragoza
            'GRX': (37.1886, -3.7770),  # Granada
            'XRY': (36.7446, -6.0601),  # Jerez
            'REU': (41.1474, 1.1671),   # Reus
        }
    
    async def calculate_distance(self, origen: str, destino: str, 
                               transport_type: str = 'driving') -> DistanceResult:
        """
        Calcular distancia real entre dos puntos
        
        Args:
            origen: Código de provincia, aeropuerto o coordenadas
            destino: Código de provincia, aeropuerto o coordenadas  
            transport_type: 'driving', 'transit', 'walking'
        """
        
        # Crear clave de cache
        cache_key = f"{origen}-{destino}-{transport_type}"
        
        if cache_key in self._distance_cache:
            logger.debug(f"📋 Distancia desde cache: {origen} → {destino}")
            return self._distance_cache[cache_key]
        
        try:
            # Obtener coordenadas
            origen_coords = self._get_coordinates(origen)
            destino_coords = self._get_coordinates(destino)
            
            if not origen_coords or not destino_coords:
                return DistanceResult(
                    distance_km=0,
                    duration_minutes=0,
                    route_type=transport_type,
                    api_used="none",
                    success=False,
                    error_message="No se pudieron obtener coordenadas"
                )
            
            # Intentar APIs en orden de preferencia
            result = None
            
            # 1. OpenRouteService (gratuita, 2000 requests/día)
            if not result:
                result = await self._calculate_with_openroute(
                    origen_coords, destino_coords, transport_type
                )
            
            # 2. GraphHopper (gratuita, 2500 requests/día)
            if not result or not result.success:
                result = await self._calculate_with_graphhopper(
                    origen_coords, destino_coords, transport_type
                )
            
            # 3. Fallback: cálculo aproximado
            if not result or not result.success:
                result = self._calculate_approximate(
                    origen_coords, destino_coords, transport_type
                )
            
            # Guardar en cache
            self._distance_cache[cache_key] = result
            
            logger.info(f"🗺️ Distancia calculada: {origen} → {destino} = {result.distance_km}km")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error calculando distancia {origen} → {destino}: {e}")
            return DistanceResult(
                distance_km=0,
                duration_minutes=0,
                route_type=transport_type,
                api_used="error",
                success=False,
                error_message=str(e)
            )
    
    def _get_coordinates(self, location: str) -> Optional[Tuple[float, float]]:
        """Obtener coordenadas de una ubicación"""
        
        # Verificar si es código de provincia
        if location in self.provincia_coords:
            return self.provincia_coords[location]
        
        # Verificar si es código de aeropuerto
        if location in self.airport_coords:
            return self.airport_coords[location]
        
        # Si es una cadena con coordenadas "lat,lon"
        if ',' in location:
            try:
                lat, lon = location.split(',')
                return (float(lat), float(lon))
            except:
                pass
        
        return None
    
    async def _calculate_with_openroute(self, origen: Tuple[float, float], 
                                      destino: Tuple[float, float], 
                                      transport_type: str) -> Optional[DistanceResult]:
        """Calcular distancia usando OpenRouteService API (gratuita)"""
        
        try:
            # Mapear tipos de transporte
            profile_map = {
                'driving': 'driving-car',
                'transit': 'driving-car',  # Fallback a coche
                'walking': 'foot-walking'
            }
            
            profile = profile_map.get(transport_type, 'driving-car')
            
            url = f"https://api.openrouteservice.org/v2/directions/{profile}"
            
            headers = {
                'Authorization': settings.OPENROUTE_API_KEY if hasattr(settings, 'OPENROUTE_API_KEY') else '',
                'Content-Type': 'application/json'
            }
            
            # Coordenadas en formato [lon, lat] para OpenRouteService
            coordinates = [
                [origen[1], origen[0]],  # [lon, lat]
                [destino[1], destino[0]]
            ]
            
            data = {
                'coordinates': coordinates,
                'format': 'json'
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(url, headers=headers, json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if 'routes' in result and len(result['routes']) > 0:
                        route = result['routes'][0]
                        distance_m = route['summary']['distance']
                        duration_s = route['summary']['duration']
                        
                        return DistanceResult(
                            distance_km=int(distance_m / 1000),
                            duration_minutes=int(duration_s / 60),
                            route_type=transport_type,
                            api_used="openroute",
                            success=True
                        )
                
        except Exception as e:
            logger.warning(f"⚠️ Error con OpenRouteService: {e}")
        
        return None
    
    async def _calculate_with_graphhopper(self, origen: Tuple[float, float], 
                                        destino: Tuple[float, float], 
                                        transport_type: str) -> Optional[DistanceResult]:
        """Calcular distancia usando GraphHopper API (gratuita)"""
        
        try:
            # Mapear tipos de transporte
            vehicle_map = {
                'driving': 'car',
                'transit': 'car',
                'walking': 'foot'
            }
            
            vehicle = vehicle_map.get(transport_type, 'car')
            
            url = "https://graphhopper.com/api/1/route"
            
            params = {
                'point': [f"{origen[0]},{origen[1]}", f"{destino[0]},{destino[1]}"],
                'vehicle': vehicle,
                'key': settings.GRAPHHOPPER_API_KEY if hasattr(settings, 'GRAPHHOPPER_API_KEY') else '',
                'calc_points': 'false'
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url, params=params)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if 'paths' in result and len(result['paths']) > 0:
                        path = result['paths'][0]
                        distance_m = path['distance']
                        duration_ms = path['time']
                        
                        return DistanceResult(
                            distance_km=int(distance_m / 1000),
                            duration_minutes=int(duration_ms / 60000),
                            route_type=transport_type,
                            api_used="graphhopper",
                            success=True
                        )
                
        except Exception as e:
            logger.warning(f"⚠️ Error con GraphHopper: {e}")
        
        return None
    
    def _calculate_approximate(self, origen: Tuple[float, float], 
                             destino: Tuple[float, float], 
                             transport_type: str) -> DistanceResult:
        """Calcular distancia aproximada usando fórmula de Haversine"""
        
        try:
            lat1, lon1 = origen
            lat2, lon2 = destino
            
            # Fórmula de Haversine
            R = 6371  # Radio de la Tierra en km
            
            dlat = math.radians(lat2 - lat1)
            dlon = math.radians(lon2 - lon1)
            
            a = (math.sin(dlat/2) * math.sin(dlat/2) + 
                 math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) * 
                 math.sin(dlon/2) * math.sin(dlon/2))
            
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            distance = R * c
            
            # Factor de corrección según tipo de transporte
            if transport_type == 'driving':
                distance *= 1.3  # +30% por carreteras
            elif transport_type == 'transit':
                distance *= 1.2  # +20% por transporte público
            else:
                distance *= 1.1  # +10% por rutas peatonales
            
            # Tiempo estimado
            speed_map = {
                'driving': 80,    # 80 km/h promedio
                'transit': 50,    # 50 km/h promedio
                'walking': 5      # 5 km/h
            }
            
            speed = speed_map.get(transport_type, 80)
            duration_minutes = int((distance / speed) * 60)
            
            return DistanceResult(
                distance_km=int(distance),
                duration_minutes=duration_minutes,
                route_type=transport_type,
                api_used="haversine",
                success=True
            )
            
        except Exception as e:
            logger.error(f"❌ Error en cálculo aproximado: {e}")
            return DistanceResult(
                distance_km=500,  # Distancia por defecto
                duration_minutes=360,  # 6 horas por defecto
                route_type=transport_type,
                api_used="default",
                success=False,
                error_message=str(e)
            )
    
    async def calculate_transfer_distance(self, aeropuerto_o_estacion: str, 
                                        hotel_coords: Tuple[float, float]) -> DistanceResult:
        """Calcular distancia de traslado aeropuerto/estación ↔ hotel"""
        
        # Obtener coordenadas del aeropuerto/estación
        transport_coords = self._get_coordinates(aeropuerto_o_estacion)
        
        if not transport_coords:
            return DistanceResult(
                distance_km=15,  # Distancia por defecto
                duration_minutes=30,
                route_type='driving',
                api_used="default",
                success=False,
                error_message="No se encontraron coordenadas del aeropuerto/estación"
            )
        
        # Calcular distancia real
        return await self.calculate_distance(
            f"{transport_coords[0]},{transport_coords[1]}",
            f"{hotel_coords[0]},{hotel_coords[1]}",
            'driving'
        )

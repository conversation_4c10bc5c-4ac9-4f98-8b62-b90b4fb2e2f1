/**
 * This file contains all needed classes and utils to perform
 * error logging operations such as reporting and handling.
 */


/**
 * Represents a button to specific error.
 */
class RowButton {
    /**
    * @param {number} colIndex  Column index where this error lives
    * @param {number} rowIndex  Row index where this error lives
    * @param {String} label     Label for this error, usually is the header for this column
    */
    constructor(colIndex, rowIndex, label){
        this.colIndex = colIndex;
        this.rowIndex = rowIndex;
        this.label = label;
    }
    /**
     * Returns the html code for generating this button
     * @returns {String}    Html code to display this button
     */
    html(){
        return `<button onclick="google.script.run.moveToRange(${this.rowIndex}, ${this.colIndex})">${this.label}</button>`
    }

    /**
     * Returns a unique id for this button
     * @returns {String}    Unique button id
     */
    id(){
        return `${this.colIndex}.${this.rowIndex}.${this.label}`
    }
}

/**
 * Represents a global error on the spreasheet.
 */
class GlobalErrorEntry {
    /**
     * Represents a global error
     * @param {String} title        A title for this error
     * @param {String} description  Description for this error
     */
    constructor(title, description) {
        this.title = title;
        this.description = description;
    }
}

/**
 * Represents an error with the specified row
 */
class RowErrorEntry {
    /**
     * Represents a row based row error
     * @param {number} row          Row number for this error (this is more an identifier to goup the error)
     * @param {String} description  Description for this error
     */
    constructor(row, description){
        this.row = row;
        this.description = description;
        this.buttons = [];
    }

    /**
     * Add a button to this specific error
     * @param {number} col      Column index for this button
     * @param {String} label    Label for this button
     * @returns {RowErrorEntry} Return this exact object, so you can continue adding things
     */
    addRowButton(col, label){
        this.buttons.push(new RowButton(col, this.row, label));
        return this;
    }
}

/**
 * The error manager helps you to keep track of all the errors that ocurred during
 * a spreadsheet validation.
 * 
 * It can handle 2 different types of errors:
 * - GlobalErrorEntry
 * - RowErrorEntry
 */
class ErrorManager {
    constructor(){
        this.rowErrors = {};  // This contains all the row based errors, it is a dictionary with lists
        this.globalErrors = [];  // Contains a list with all the global errors for those validations
    }

    /**
     * Add a generic error entry to this manager
     * @param {string} title
     * @param {string} description
     */
    addGlobalError(title, description){
        let gError = new GlobalErrorEntry(title, description)
        this.globalErrors.push(gError);
    }

    /**
     * Add a rowbased error to this manager
     * @param {RowErrorEntry} rError 
     */
    addRowError(row, description){
        let rError = new RowErrorEntry(row, description, this);
        if (rError.row in this.rowErrors){
            this.rowErrors[rError.row].push(rError);
        }else{
            this.rowErrors[rError.row] = [rError];
        }
        return rError;
    }

    /**
     * Converts this object into an Html string that could be displayed using HtmlService
     * @returns {String} Html string to be shown
     */
    html(){
        let output = "";
        for (var key in this.rowErrors){
            let buttons_processed = []
            let buttons_html = "";
            let rowsErrors = this.rowErrors[key];
            output = output + `\t<p> Fila ${key}</p>\n`;
            output = output + `\t<ul>\n`;
            rowsErrors.forEach(rowError => {
                output = output + `\t\t<li> ${rowError.description}</li>\n`
                rowError.buttons.forEach(button => {
                    // Process buttons only once
                    if(buttons_processed.indexOf(button.id()) == -1){
                        buttons_processed.push(button.id());
                        buttons_html = buttons_html + button.html();
                    }
                })
            });
            output = output + `\t</ul>\n\t${buttons_html}\n`
        }
        for (let key in this.globalErrors){
            let globalError = this.globalErrors[key]
            output = output + `\t<p> ${globalError.title}</p>\n`
            output = output + `\t<p> ${globalError.description}</p>\n`
        }
        return output;
    }

    /**
     * 
     */
    failedCells(){
        let errored_cells = [];
        for (var rowIndex in this.rowErrors){
            let rowErrors = this.rowErrors[rowIndex];
            let alreadyProccessedColIndexes = [];
            rowErrors.forEach(rowError =>{
                rowError.buttons.forEach(button =>{
                    if(alreadyProccessedColIndexes.indexOf(button.colIndex) == -1){
                        alreadyProccessedColIndexes.push(button.colIndex);
                        errored_cells.push([button.rowIndex, button.colIndex]);
                    }
                });
            });
        }
        return errored_cells;
    }
}




function markColumnsAsErrors(rowIndexInSheet, failed_cols){
    failed_cols.forEach((colIndex) =>{
      sheet.getRange(rowIndexInSheet, colIndex+1)
           .setFontWeight(settings.errorFontWeight)
           .setFontColor(settings.errorFontColor);
    })
}

function markColumnAsError(rowIndexInSheet, colIndexInSheet){
  Logger.log(rowIndexInSheet);
  Logger.log(colIndexInSheet);
  sheet.getRange(rowIndexInSheet, colIndexInSheet)
       .setFontWeight(settings.errorFontWeight)
       .setFontColor(settings.errorFontColor);
}


/**
 * This demonstrate an example of how it works
 */

/*
var manager = new ErrorManager();
manager.addRowError(1, "Un puto error").addRowButton(10, "Blab").addRowButton(10, "Blab");
manager.addRowError(2, "Otro puto error");
manager.addRowError(2, "Un puto error en otro lado");
manager.addRowError(3, "Un puto error en otro puto sitio").addRowButton(20, "Claro claro");
manager.addGlobalError("Ojo con esto", "Que es una mierda xD");

//console.log(manager.html());
console.log(manager.failedCells());
*/